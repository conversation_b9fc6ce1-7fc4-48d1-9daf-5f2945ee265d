#!/usr/bin/env nextflow

// ====================================================================================
// CELL RANGER MULTI PIPELINE
// ====================================================================================

// Parameters
params.help = false

// Reference paths
params.gex_reference = "/rsrch3/home/<USER>/vilarsanchez/reference_new/cellranger/refdata-gex-GRCh38-2024-A"
params.vdj_reference = "/rsrch3/home/<USER>/vilarsanchez/reference_new/cellranger/refdata-cellranger-vdj-GRCh38-alts-ensembl-7.1.0"

// Output directory
params.output_dir = "result/cellranger_multi"

// Sample data - defined as parameters for easy modification
params.samples = [
    [
        sample_id: "SC415_2",
        scrna_sample: "SC415_scRNA_2",
        tcr_sample: "SC415_TCR_2",
        scrna_path: "/rsrch8/home/<USER>/vilarsanchez/data/FD_scRNA_TCR/SC415/Fastq/scRNA/SC415_scRNA_2",
        tcr_path: "/rsrch8/home/<USER>/vilarsanchez/data/FD_scRNA_TCR/SC415/Fastq/TCR/SC415_TCR_2",
    ],
    [
        sample_id: "SC555_1",
        scrna_sample: "SC555_1",
        tcr_sample: "SC555_TCR_1",
        scrna_path: "/rsrch8/home/<USER>/vilarsanchez/data/SC555/scRNA/Fastq/SC555_1",
        tcr_path: "/rsrch8/home/<USER>/vilarsanchez/data/SC555/TCR/Fastq/SC555_TCR_1",
    ],
    [
        sample_id: "SC327_3",
        scrna_sample: "SC327_scRNA_3",
        tcr_sample: "SC327_TCR_3",
        scrna_path: "/rsrch8/home/<USER>/vilarsanchez/data/FD_scRNA_TCR/SC327/scRNA/Fastq/SC327_scRNA_3",
        tcr_path: "/rsrch8/home/<USER>/vilarsanchez/data/FD_scRNA_TCR/SC327/scTCR/Fastq/SC327_TCR_3/",
    ],
]

// Help message
def helpMessage() {
    log.info(
        """
    Cell Ranger Multi Pipeline
    ==========================
    
    Usage:
        nextflow run cellranger_multi_pipeline.nf [options]
    
    Parameters:
        --gex_reference     Path to Cell Ranger GEX reference (default: ${params.gex_reference})
        --vdj_reference     Path to Cell Ranger VDJ reference (default: ${params.vdj_reference})
        --output_dir        Output directory (default: ${params.output_dir})
    
    Samples:
        SC415_2: scRNA + TCR data
        SC555_1: scRNA + TCR data
    
    Example:
        nextflow run cellranger_multi_pipeline.nf
    """.stripIndent()
    )
}

// ====================================================================================
// PROCESSES
// ====================================================================================

process createMultiConfig {
    tag "Creating config for ${sample_id}"

    executor 'lsf'
    queue 'short'
    memory '4.GB'
    cpus 1
    time '30m'
    errorStrategy 'retry'
    maxRetries 2

    input:
    tuple val(sample_id), val(scrna_sample), val(tcr_sample), val(scrna_path), val(tcr_path)

    output:
    tuple val(sample_id), path("${sample_id}_config.csv")

    script:
    """
    cat > ${sample_id}_config.csv << EOF
[gene-expression]
reference,${params.gex_reference}
chemistry,auto
expect-cells,10000
create-bam,true

[vdj]
reference,${params.vdj_reference}

[libraries]
fastq_id,fastqs,feature_types
${scrna_sample},${scrna_path},Gene Expression
${tcr_sample},${tcr_path},VDJ-T
EOF
    """
}

process runCellRangerMulti {
    tag "Cell Ranger multi for ${sample_id}"
    publishDir "${params.output_dir}", mode: 'copy'

    executor 'lsf'
    queue 'medium'
    memory '128.GB'
    cpus 28
    time '24h'
    module 'cellranger/9.0.1'

    input:
    tuple val(sample_id), path(config_file)

    output:
    tuple val(sample_id), path("${sample_id}")

    script:
    """
    cellranger multi \\
        --id=${sample_id} \\
        --csv=${config_file} \\
        --localcores=${task.cpus} \\
        --localmem=\$(echo "${task.memory}" | sed 's/[^0-9]//g')
    """
}

process generateSummaryReport {
    tag "Generating summary report"
    publishDir "${params.output_dir}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '8.GB'
    cpus 1
    time '1h'
    errorStrategy 'retry'
    maxRetries 2

    input:
    path all_results

    output:
    path "cellranger_multi_summary.html"

    script:
    """
    cat > cellranger_multi_summary.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Cell Ranger Multi Summary</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #2c3e50; }
        h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 10px; }
        .sample { margin: 20px 0; padding: 20px; border: 1px solid #bdc3c7; border-radius: 5px; }
        .path { font-family: monospace; background-color: #f8f9fa; padding: 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Cell Ranger Multi Analysis Summary</h1>
    <p>Analysis completed on: \$(date)</p>
    
    <h2>Processed Samples</h2>
EOF

    for result_dir in */; do
        if [ -d "\$result_dir" ]; then
            sample_name=\$(basename "\$result_dir")
            echo "<div class='sample'>" >> cellranger_multi_summary.html
            echo "<h3>\$sample_name</h3>" >> cellranger_multi_summary.html
            echo "<p><strong>Output directory:</strong> <span class='path'>\$result_dir</span></p>" >> cellranger_multi_summary.html
            
            # Check for key output files
            if [ -f "\$result_dir/outs/per_sample_outs/\$sample_name/web_summary.html" ]; then
                echo "<p>✓ Web summary available</p>" >> cellranger_multi_summary.html
            fi
            if [ -f "\$result_dir/outs/per_sample_outs/\$sample_name/count/sample_filtered_feature_bc_matrix.h5" ]; then
                echo "<p>✓ Gene expression matrix available</p>" >> cellranger_multi_summary.html
            fi
            if [ -f "\$result_dir/outs/per_sample_outs/\$sample_name/vdj_t/filtered_contig_annotations.csv" ]; then
                echo "<p>✓ TCR annotations available</p>" >> cellranger_multi_summary.html
            fi
            echo "</div>" >> cellranger_multi_summary.html
        fi
    done

    cat >> cellranger_multi_summary.html << 'EOF'
    
    <h2>Analysis Details</h2>
    <ul>
        <li><strong>Gene Expression Reference:</strong> ${params.gex_reference}</li>
        <li><strong>VDJ Reference:</strong> ${params.vdj_reference}</li>
        <li><strong>Cell Ranger Version:</strong> 9.0.1</li>
    </ul>
    
    <h2>Next Steps</h2>
    <p>For each sample, you can find:</p>
    <ul>
        <li>Web summary: <code>[sample]/outs/per_sample_outs/[sample]/web_summary.html</code></li>
        <li>Gene expression matrix: <code>[sample]/outs/per_sample_outs/[sample]/count/sample_filtered_feature_bc_matrix.h5</code></li>
        <li>TCR annotations: <code>[sample]/outs/per_sample_outs/[sample]/vdj_t/filtered_contig_annotations.csv</code></li>
    </ul>
</body>
</html>
EOF
    """
}

// ====================================================================================
// MAIN WORKFLOW
// ====================================================================================

workflow {

    // Show help message if requested
    if (params.help) {
        helpMessage()
        exit(0)
    }

    // Validate required parameters
    if (!params.gex_reference) {
        log.error("ERROR: --gex_reference parameter is required")
        exit(1)
    }
    if (!params.vdj_reference) {
        log.error("ERROR: --vdj_reference parameter is required")
        exit(1)
    }

    // Create input channel from sample data
    samples_ch = Channel.fromList(params.samples)
        .map { sample ->
            tuple(sample.sample_id, sample.scrna_sample, sample.tcr_sample, sample.scrna_path, sample.tcr_path)
        }

    // Step 1: Create Cell Ranger multi configuration files
    config_ch = createMultiConfig(samples_ch)

    // Step 2: Run Cell Ranger multi
    results_ch = runCellRangerMulti(config_ch)

    // Step 3: Generate summary report
    all_results_ch = results_ch.map { sample_id, result_dir -> result_dir }.collect()
    generateSummaryReport(all_results_ch)
}
