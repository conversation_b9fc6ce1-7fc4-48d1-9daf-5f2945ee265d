# ===================================================================
# R SCRIPT FOR QUALIMAP RNA-SEQ QC ANALYSIS AND VISUALIZATION
# ===================================================================

# --- 1. SETUP: INSTALL AND LOAD REQUIRED PACKAGES ---

# The 'tidyverse' is a collection of R packages designed for data science.
# It includes ggplot2, dplyr, readr, purrr, and stringr, which are all used in this script.
# If you haven't installed it yet, uncomment and run the line below.
# install.packages("tidyverse")

# Load the tidyverse library for access to all its functions
library(tidyverse)
library(ggpubr)

# --- 2. CORE PARSING FUNCTION ---

#' Parse a single Qualimap rnaseq_qc_results.txt file
#'
#' @param file_path The full path to the rnaseq_qc_results.txt file.
#' @return A single-row tibble containing key QC metrics.
parse_qualimap_rnaseq <- function(file_path) {
    # Read the entire file into a character vector, one element per line
    lines <- readr::read_lines(file_path)

    # Helper function to extract a numeric value from a line based on a pattern
    # It handles numbers with commas.
    extract_value <- function(lines, pattern) {
        # Find the line that contains the pattern
        line <- stringr::str_subset(lines, pattern)
        # If a line is found, extract the numeric part
        if (length(line) > 0) {
            # Extract numbers, allowing for commas
            value_str <- stringr::str_extract(line, "[0-9,]+")
            # Remove commas and convert to a numeric type
            as.numeric(stringr::str_replace_all(value_str, ",", ""))
        } else {
            # Return NA if the pattern is not found
            NA_real_
        }
    }

    # Helper function to extract a percentage value from a line
    extract_percentage <- function(lines, pattern) {
        line <- stringr::str_subset(lines, pattern)
        if (length(line) > 0) {
            # Extract the number within parentheses, including the % sign
            value_str <- stringr::str_extract(line, "\\([0-9.]+\\%\\)")
            # Remove parentheses and '%' sign, then convert to numeric
            as.numeric(stringr::str_replace_all(value_str, "[\\(\\)%]", ""))
        } else {
            NA_real_
        }
    }

    # Helper function to extract the 5'-3' bias value
    extract_bias <- function(lines, pattern) {
        line <- stringr::str_subset(lines, pattern)
        if (length(line) > 0) {
            # Extract the floating point number after the '=' sign
            value_str <- stringr::str_extract(line, "[0-9]+\\.[0-9]+")
            as.numeric(value_str)
        } else {
            NA_real_
        }
    }

    # Extract all key metrics using the helper functions
    # >>>>>>> Reads alignment section
    reads_aligned <- extract_value(lines, "^\\s+reads aligned")
    total_alignments <- extract_value(lines, "^\\s+total alignments")
    secondary_alignments <- extract_value(lines, "^\\s+secondary alignments")
    non_unique_alignments <- extract_value(lines, "^\\s+non-unique alignments")
    aligned_to_genes <- extract_value(lines, "^\\s+aligned to genes")
    ambiguous_alignments <- extract_value(lines, "^\\s+ambiguous alignments")
    no_feature_assigned <- extract_value(lines, "^\\s+no feature assigned")
    reads_not_aligned <- extract_value(lines, "^\\s+not aligned")

    # >>>>>>> Reads genomic origin section
    percent_exonic <- extract_percentage(lines, "^\\s+exonic")
    percent_intronic <- extract_percentage(lines, "^\\s+intronic")
    percent_intergenic <- extract_percentage(lines, "^\\s+intergenic")

    # >>>>>>> Transcript coverage profile section
    bias_5_3 <- extract_bias(lines, "^\\s+5'-3' bias")

    # Create a single-row tibble with the extracted data
    tibble::tibble(
        file_path = file_path,
        reads_aligned = reads_aligned,
        total_alignments = total_alignments,
        secondary_alignments = secondary_alignments,
        non_unique_alignments = non_unique_alignments,
        aligned_to_genes = aligned_to_genes,
        ambiguous_alignments = ambiguous_alignments,
        no_feature_assigned = no_feature_assigned,
        reads_not_aligned = reads_not_aligned,
        percent_exonic = percent_exonic,
        percent_intronic = percent_intronic,
        percent_intergenic = percent_intergenic,
        bias_5_3 = bias_5_3
    )
}


# --- 3. BATCH PROCESSING AND DATA AGGREGATION ---

# Define the path to the parent directory containing all sample folders
# IMPORTANT: Change "qualimap_rnaseq" to the actual name of your top-level folder.
parent_dir <- "qualimap_rnaseq"

# Find all 'rnaseq_qc_results.txt' files recursively within the parent directory
qualimap_files <- list.files(
    path = parent_dir,
    pattern = "rnaseq_qc_results.txt",
    recursive = TRUE,
    full.names = TRUE
)

# Apply the parsing function to each file and combine the results into a single data frame
# map_dfr is used because our function returns a data frame (tibble)
qc_summary_raw <- purrr::map_dfr(qualimap_files, parse_qualimap_rnaseq)

# --- Data Cleaning and Feature Engineering ---

# Clean up the data and create new, more interpretable metrics
qc_summary <- qc_summary_raw %>%
    # Extract a clean sample ID from the file path
    # This assumes the sample ID is the name of the folder containing the results file
    dplyr::mutate(
        sample_id = stringr::str_extract(file_path, "(?<=qualimap_rnaseq/)[^/]+"),
        .before = 1
    ) %>%
    # Calculate key percentage-based metrics for easier comparison
    dplyr::mutate(
        total_reads = reads_aligned + reads_not_aligned,
        percent_aligned = (reads_aligned / total_reads) * 100,
        # Of the aligned reads, what percentage was assigned to genes?
        percent_assigned_to_genes = (aligned_to_genes / reads_aligned) * 100,
        # Of the aligned reads, what percentage was non-unique?
        percent_non_unique = (non_unique_alignments / reads_aligned) * 100
    ) %>%
    # Select and reorder columns for clarity
    dplyr::select(
        sample_id,
        total_reads,
        reads_aligned,
        percent_aligned,
        aligned_to_genes,
        percent_assigned_to_genes,
        non_unique_alignments,
        percent_non_unique,
        no_feature_assigned,
        reads_not_aligned,
        percent_exonic,
        percent_intronic,
        percent_intergenic,
        bias_5_3
    )

# Display the final aggregated data frame in the console
print(qc_summary)

qc_summary <- distinct(qc_summary) %>% drop_na()
# --- 4. VISUALIZATION OF QC METRICS ---

# --- Plot 4.1: Overall Mapping Rate (% Aligned Reads) ---
thresholds_aligned <- tibble::tribble(
    ~threshold, ~ystart, ~yend, ~fill,
    "Problematic", 0, 70, "#F8766D",
    "Acceptable", 70, 90, "#619CFF",
    "Ideal", 90, 100, "#00BA38"
)


ggplot(qc_summary, aes(x = sample_id, y = percent_aligned)) +
    geom_rect(
        data = thresholds_aligned, inherit.aes = FALSE,
        aes(xmin = -Inf, xmax = Inf, ymin = ystart, ymax = yend, fill = fill),
        alpha = 0.2
    ) +
    geom_bar(stat = "identity", fill = "gray50", color = NA) +
    geom_text(aes(label = sprintf("%.1f%%", percent_aligned)), vjust = -0.5, size = 3) +
    coord_cartesian(ylim = c(0, 100)) +
    geom_hline(yintercept = 100, linetype = "dotted", color = "black") +
    scale_fill_manual(
        name = "Quality Range",
        values = c("#00BA38", "#619CFF", "#F8766D"),
        labels = c("Ideal (>90%)", "Acceptable (70-90%)", "Problematic (<70%)")
    ) +
    labs(
        title = "Overall Mapping Rate Across Samples",
        subtitle = "Percentage of total reads successfully aligned to the reference genome",
        x = "Sample ID",
        y = "Percent Aligned (%)"
    ) +
    theme_bw() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
ggsave("Percentage_algined.pdf", height = 5, width = 7)

# --- Plot 4.2: Gene Assignment Rate (% Reads Aligned to Genes) ---
thresholds_assigned <- tibble::tribble(
    ~threshold, ~ystart, ~yend, ~fill,
    "Low", 0, 50, "#F8766D",
    "Good", 50, 70, "#619CFF",
    "Excellent", 70, 100, "#00BA38"
)

ggplot(qc_summary, aes(x = sample_id, y = percent_assigned_to_genes)) +
    geom_rect(
        data = thresholds_assigned, inherit.aes = FALSE,
        aes(xmin = -Inf, xmax = Inf, ymin = ystart, ymax = yend, fill = fill),
        alpha = 0.2
    ) +
    geom_bar(stat = "identity", fill = "gray50", color = NA) +
    geom_text(aes(label = sprintf("%.1f%%", percent_assigned_to_genes)), vjust = -0.5, size = 3) +
    coord_cartesian(ylim = c(0, 105)) +
    scale_fill_manual(
        name = "Quality Range",
        values = c("#F8766D", "#00BA38", "#619CFF"),
        labels = c("Low (<50%)", "Good (50-70%)", "Excellent (>70%)")
    ) +
    labs(
        title = "Gene Assignment Rate Across Samples",
        subtitle = "Percentage of aligned reads that overlap annotated gene features",
        x = "Sample ID",
        y = "Percent Assigned to Genes (%)"
    ) +
    theme_bw() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
ggsave("Percentage_assigned.pdf", height = 5, width = 7)


# --- Plot 4.3: Distribution of Read Mapping Fates ---
stacked_data <- qc_summary %>%
    dplyr::mutate(
        unique_reads = reads_aligned - non_unique_alignments
    ) %>%
    dplyr::select(sample_id, unique_reads, non_unique_alignments, reads_not_aligned) %>%
    tidyr::pivot_longer(
        cols = -sample_id,
        names_to = "category",
        values_to = "count"
    ) %>%
    dplyr::mutate(
        category = factor(category, levels = c("reads_not_aligned", "non_unique_alignments", "unique_reads"))
    )

ggplot(stacked_data, aes(x = sample_id, y = count, fill = category)) +
    geom_bar(stat = "identity", position = "fill", color = NA) +
    scale_y_continuous(labels = scales::percent) +
    scale_fill_manual(
        name = "Read Fate",
        values = c("unique_reads" = "#619CFF", "non_unique_alignments" = "#00BA38", "reads_not_aligned" = "#F8766D"),
        labels = c("Uniquely Mapped", "Non-Uniquely Mapped", "Not Aligned")
    ) +
    labs(
        title = "Distribution of Read Mapping Fates",
        subtitle = "Proportion of reads that mapped uniquely, non-uniquely, or failed to align",
        x = "Sample ID",
        y = "Percentage of Total Reads"
    ) +
    theme_bw() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))


# --- Plot 4.4: Genomic Origin of Aligned Reads ---
origin_data <- qc_summary %>%
    dplyr::select(sample_id, percent_exonic, percent_intronic, percent_intergenic) %>%
    tidyr::pivot_longer(
        cols = -sample_id,
        names_to = "origin",
        values_to = "percentage"
    ) %>%
    dplyr::mutate(
        origin = stringr::str_replace(origin, "percent_", ""),
        origin = factor(origin, levels = c("intergenic", "intronic", "exonic"))
    )

ggplot(origin_data, aes(x = sample_id, y = percentage, fill = origin)) +
    geom_bar(stat = "identity", position = "stack", color = NA) +
    scale_y_continuous(labels = scales::percent_format(scale = 1)) +
    scale_fill_manual(
        name = "Genomic Origin",
        values = c("exonic" = "#619CFF", "intronic" = "#00BA38", "intergenic" = "#F8766D"),
        labels = c("Exonic", "Intronic", "Intergenic")
    ) +
    labs(
        title = "Genomic Origin of Aligned Reads",
        subtitle = "Distribution of reads across exonic, intronic, and intergenic regions",
        x = "Sample ID",
        y = "Percentage of Mapped Reads"
    ) +
    theme_bw() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
ggsave("origin_data.pdf", height = 5, width = 7)

# --- Plot 4.5: Transcript Coverage Bias (5'-3' Ratio) ---
ggplot(qc_summary, aes(x = sample_id, y = bias_5_3)) +
    geom_hline(yintercept = 1, linetype = "dashed", color = "blue", size = 1) +
    geom_bar(stat = "identity", fill = "gray50", color = "black") +
    coord_cartesian(ylim = c(0, max(qc_summary$bias_5_3, na.rm = TRUE) * 1.2)) +
    labs(
        title = "Transcript Coverage Bias (5'-3' Ratio)",
        subtitle = "Ratio of 5' to 3' coverage. A value of 1 indicates uniform coverage.",
        x = "Sample ID",
        y = "5'-3' Bias Ratio"
    ) +
    theme_bw() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
ggsave("3prime_5prime_bias.pdf", height = 5, width = 7)

# --- END OF SCRIPT ---
