library(tidyverse)
source("function.R")

library(ggpubr)
library(vcfR)


vcf <- read.vcfR("./dragen_vcf/S22-E1-Ade/S22-E1-Ade.dragen.filtered.vcf.gz")

# Filter VCF for PASS variants and extract basic variant info

extract_pass <- function(vcf_path) {
    vcf <- read.vcfR(vcf_path)
    vcf_filtered <- vcf[vcf@fix[, "FILTER"] == "PASS", ]

    vcf_tibble <- tibble(
        CHROM = vcf_filtered@fix[, "CHROM"],
        POS = as.numeric(vcf_filtered@fix[, "POS"]),
        ALT = vcf_filtered@fix[, "ALT"]
    )
}

meta <- read_csv("./meta/meta.csv")

single_call <- dir("./dragen_vcf/", recursive = T, full.names = T) %>%
    str_subset("filtered.vcf.gz$") %>%
    map_dfr(
        ~ extract_pass(.x) %>%
            mutate(
                sample = basename(.x) %>% str_split_1("\\.") %>% `[`(1),
                type = "single_call"
            )
    ) %>%
    unite(variant_id, CHROM, POS, ALT)

single_call %>%
    count(sample) %>%
    mutate(type = str_extract(sample, "Nm|Ade")) %>%
    left_join(meta %>% pivot_longer(contains("WEX")) %>% distinct(Sample, value),
        by = c("sample" = "value")
    ) %>%
    ggbarplot(x = "Sample", y = "n", fill = "type", position = position_dodge2()) +
    rotate_x_text()

ggsave("single_call.png", width = 7, height = 7)

somatic <- dir("./final_somatic_vcfs/", recursive = T, full.names = T) %>%
    str_subset("vep.vcf$") %>%
    map_dfr(
        ~ parse_vcf_csq(.x) %>%
            select(CHROM, POS, REF, ALT, CALLER) %>%
            mutate(
                sample = basename(.x) %>% str_split_1("\\.") %>% `[`(1),
                type = "somatic"
            )
    ) %>%
    unite(variant_id, CHROM, POS, ALT)

germline <- list.files("./germline_vcf", pattern = "germline.trimmed.vcf.gz$", full.names = T) %>%
    map_dfr(
        ~ extract_pass(.x) %>%
            mutate(
                sample = basename(.x) %>% str_split_1("\\.") %>% `[`(1),
                type = "germline"
            )
    ) %>%
    unite(variant_id, CHROM, POS, ALT)





bind_rows(single_call, germline) %>%
    distinct() %>%
    dplyr::filter(str_detect(sample, "Nm")) %>%
    summarise(
        type = str_c(type %>% unique() %>% sort(), collapse = ","),
        .by = c(sample, variant_id)
    ) %>%
    count(sample, type) %>%
    ggbarplot(
        x = "sample",
        y = "n",
        fill = "type"
    ) +
    rotate_x_text()

ggsave("germline_call_count.png", width = 7, height = 7)


single_somatic_to_normal <-
    single_call %>%
    mutate(type = str_extract(sample, "Nm|Ade")) %>%
    left_join(meta %>% pivot_longer(contains("WEX")) %>% distinct(Sample, value),
        by = c("sample" = "value")
    ) %>%
    mutate(
        sample = factor(sample) %>% as.numeric(),
        type_2 = case_when(
            max(sample) > 1 ~ str_c(type, "_", sample),
            T ~ type
        ),
        .by = c(Sample, type)
    ) %>%
    pivot_wider(
        id_cols = c(variant_id, Sample),
        names_from = type,
        values_from = type_2,
        values_fn = ~ unique(.x) %>%
            sort() %>%
            str_c(collapse = ","),
        values_fill = NA
    ) %>%
    rename(
        Single_Tumor_Call = Ade,
        Normal_Call = Nm
    ) %>%
    full_join(
        somatic %>%
            summarise(
                TN_pair_call = str_c(CALLER %>% unique() %>% sort(), collapse = ","),
                .by = c(sample, variant_id)
            ),
        by = c("variant_id", "Sample" = "sample")
    )
# =============================================================================
# CLEAN SANKEY PLOT FOR YOUR DATA
# =============================================================================

single_somatic_to_normal %>%
    group_by(Sample) %>%
    group_map(
        \(single_somatic_to_normal, Sample){
            data <- single_somatic_to_normal %>%
                count(Normal_Call, Single_Tumor_Call, TN_pair_call) %>%
                mutate(
                    Single_Tumor_Call =
                        case_when(
                            is.na(Normal_Call) & !is.na(Single_Tumor_Call) & is.na(TN_pair_call) ~ "Unique Calling (??)",
                            is.na(Normal_Call) & !is.na(Single_Tumor_Call) & !is.na(TN_pair_call) ~ "Overlap with TN only (GOOD)",
                            !is.na(Normal_Call) & !is.na(Single_Tumor_Call) & is.na(TN_pair_call) ~ "Overlap with Normal only (FP germline,BAD)",
                            !is.na(Normal_Call) & is.na(Single_Tumor_Call) & is.na(TN_pair_call) ~ "Missed in Tumor (correct germline, GOOD)",
                            is.na(Normal_Call) & is.na(Single_Tumor_Call) & !is.na(TN_pair_call) ~ "Missed Somatic(BAD)",
                            !is.na(Normal_Call) & is.na(Single_Tumor_Call) & !is.na(TN_pair_call) ~ "Fix Error(GOOD)",
                            T ~ "ALL(BAD)"
                        )
                ) %>%
                replace_na(list(
                    Normal_Call = "Normal_missed",
                    TN_pair_call = "TN_missed"
                ))



            normal_to_tumor <- data[, c(1:2, 4)] %>% rename(source = Normal_Call, target = Single_Tumor_Call, value = n)
            tumor_to_tn <- data[, 2:4] %>% rename(source = Single_Tumor_Call, target = TN_pair_call, value = n)

            links <- bind_rows(normal_to_tumor, tumor_to_tn)

            nodes <- data.frame(name = unique(c(links$source, links$target)))

            links <- links %>%
                mutate(
                    source = match(source, nodes$name) - 1,
                    target = match(target, nodes$name) - 1
                )
            sankeyNetwork(
                Links = links,
                Nodes = nodes,
                Source = "source",
                Target = "target",
                Value = "value",
                NodeID = "name",
                fontSize = 25,
                nodeWidth = 30
            ) %>% saveWidget(
                paste0("results/", Sample$Sample, "_sankey.html"),
                selfcontained = TRUE
            )
        }
    )


list.files("./PHLAT/", recursive = T, full.names = T) %>%
    map_dfr(
        ~ read_tsv(.x) %>%
            mutate(sample = basename(.x) %>% str_remove("_HLA.sum"))
    ) %>%
    left_join(
        meta %>% pivot_longer(contains("WEX")) %>% distinct(Sample, value),
        by = c("sample" = "value")
    ) %>%
    pivot_longer(
        contains("Allele")
    ) %>%
    summarise(
        value = unique(value) %>% sort() %>% str_c(collapse = ","),
        .by = c(Sample, sample, Locus)
    ) %>%
    pivot_wider(
        id_cols = c(Sample, sample),
        names_from = Locus,
        values_from = value
    ) %>%
    arrange(Sample) %>%
    gt::gt()


PT_103687_E1_Ade_BL <- read_tsv("./pvactools/PT_103687_E1_Ade_BL/combined/PT_103687_E1_Ade_BL.all_epitopes.tsv")
PT_103687_E1_Ade_BL %>%
    dplyr::filter(`Tumor DNA VAF` > 0.1, `Median MT IC50 Score` < 100, `Median Fold Change` > 10 | is.na(`Median Fold Change`))
PT_103690_B1_Ade_BL <- read_tsv("./pvactools/PT_103690_B1_Ade_BL/combined/PT_103690_B1_Ade_BL.all_epitopes.tsv")%>%
    dplyr::filter(`Tumor DNA VAF` > 0.1, `Median MT IC50 Score` < 100, `Median Fold Change` > 10 | is.na(`Median Fold Change`))


nrow(PT_103687_E1_Ade_BL)

as_tibble(list(PT_103687_E1_Ade_BL = 343,PT_103687_I1_Ade_BL = 283, PT_103690_B1_Ade_BL  =218, PT_103687_B1_Ade_W68 = 200))%>%
    pivot_longer(everything()) %>%
    rename(count = value,sample=name)%>%
    mutate(ratio = count/c(6162,25717,4164,10315) )%>%
    pivot_longer(-sample)%>%
    ggbarplot(x = "sample", y = "value",facet.by = "name",scales="free",position = position_dodge(),order = .$sample%>%unique%>%sort(decreasing = T))+rotate_x_text()

ggsave("pvactools.png", width = 7, height = 7)


Vl