#!/usr/bin/env nextflow

// ====================================================================================
// DRAGEN-GATK ONLY PIPELINE
// ====================================================================================

// Parameters
params.fastq_dir = "/rsrch8/home/<USER>/vilarsanchez/data/KS_Exome-Seq_HC378"
params.dragmap_reference = "/rsrch3/home/<USER>/vilarsanchez/reference_new/dragmap/hg38_from_gatk"
params.str_table_file = "/rsrch3/home/<USER>/vilarsanchez/reference_new/dragmap/hg38_from_gatk/Homo_sapiens_assembly38.str"
params.interval_dir = "/rsrch3/home/<USER>/vilarsanchez/reference_new/interval/twist/hg38_padded_seperated"

// Output directories
params.dragmap_bam_output = "result/dragmap_bam"
params.dragstr_model_output = "result/dragstr_models"
params.dragen_vcf_output = "result/dragen_vcf"
params.bcftools = "/rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools"

// Optional parameters
params.help = false

// Help message
def helpMessage() {
    log.info(
        """
    DRAGEN-GATK Only Pipeline
    =========================

    Usage:
        nextflow run dragen_gatk_only.nf [options]

    Required Parameters:
        --fastq_dir             Path to directory containing FASTQ files
        --dragmap_reference     Path to DRAGMAP reference directory
        --str_table_file        Path to STR table file
        --interval_dir          Path to directory containing interval BED files

    Output Parameters:
        --dragmap_bam_output    Output directory for DRAGMAP BAM files (default: result/dragmap_bam)
        --dragstr_model_output  Output directory for DRAGSTR models (default: result/dragstr_models)
        --dragen_vcf_output     Output directory for DRAGEN VCF files (default: result/dragen_vcf)

    Example:
        nextflow run dragen_gatk_only.nf \\
            --fastq_dir /path/to/fastq \\
            --dragmap_reference /path/to/dragmap/ref \\
            --str_table_file /path/to/str/table \\
            --interval_dir /path/to/intervals
    """.stripIndent()
    )
}



// ====================================================================================
// PROCESSES
// ====================================================================================

process dragmapAlignment {
    tag "DRAGMAP alignment for ${sample_name}"
    publishDir "${params.dragmap_bam_output}", mode: 'copy', pattern: "${sample_name}.dragmap.bam*"

    executor 'lsf'
    queue 'medium'
    memory '129.GB'
    cpus 28
    time '12h'
    module 'dragmap/1.3.0-non-conda'

    input:
    tuple val(sample_name), path(reads)

    output:
    tuple val(sample_name), path("${sample_name}.dragmap.bam"), path("${sample_name}.dragmap.bam.bai")

    script:
    def read1 = reads[0]
    def read2 = reads[1]
    """
    # DRAGMAP alignment
    dragen-os \\
        -r ${params.dragmap_reference} \\
        -1 ${read1} \\
        -2 ${read2} \\
        --num-threads ${task.cpus} \\
        | samtools view -@ ${task.cpus} -bS - \\
        | samtools sort -@ ${task.cpus} -o ${sample_name}.dragmap.bam -

    # Index the BAM file
    samtools index ${sample_name}.dragmap.bam
    """
}

process calibrateDragstrModel {
    tag "Calibrate DRAGSTR model for ${sample_name}"
    publishDir "${params.dragstr_model_output}", mode: 'copy', pattern: "*.dragstr_model.txt"

    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 4
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(bam), path(bai)

    output:
    tuple val(sample_name), path("${sample_name}.dragstr_model.txt")

    script:
    """
    set +u
    source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
    conda activate gatk4-*******
    set -u
    
    gatk CalibrateDragstrModel \\
        -R ${params.dragmap_reference}/Homo_sapiens_assembly38_masked.fasta \\
        -I ${bam} \\
        -str ${params.str_table_file} \\
        -O ${sample_name}.dragstr_model.txt
    """
}

process haplotypeCallerDragenModeParallel {
    tag "HaplotypeCaller DRAGEN mode for ${sample_name} interval ${interval_name}"

    executor 'lsf'
    queue 'medium'
    memory '64.GB'
    cpus 8
    time '24h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(bam), path(bai), path(dragstr_model), val(interval_name), path(interval_file)

    output:
    tuple val(sample_name), val(interval_name), path("${sample_name}.${interval_name}.dragen.raw.vcf.gz"), path("${sample_name}.${interval_name}.dragen.raw.vcf.gz.tbi")

    script:
    """
    set +u
    source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
    conda activate gatk4-*******
    set -u
    
    gatk HaplotypeCaller \\
        -R ${params.dragmap_reference}/Homo_sapiens_assembly38_masked.fasta \\
        -I ${bam} \\
        -O ${sample_name}.${interval_name}.dragen.raw.vcf.gz \\
        --dragen-mode true \\
        --dragstr-params-path ${dragstr_model} \\
        -L ${interval_file}
    """
}

process mergeVCFs {
    tag "Merge VCFs for ${sample_name}"
    publishDir "${params.dragen_vcf_output}/${sample_name}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 4
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(vcfs), path(tbis)

    output:
    tuple val(sample_name), path("${sample_name}.dragen.merged.vcf.gz"), path("${sample_name}.dragen.merged.vcf.gz.tbi")

    script:

    def sorted_vcfs = vcfs.sort { a, b ->
        def a_num = a.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        def b_num = b.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        return a_num <=> b_num
    }


    def vcf_args = sorted_vcfs.collect { "-I ${it}" }.join(' ')
    """
    set +u
    source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
    conda activate gatk4-*******
    set -u
    
    gatk GatherVcfs \\
        ${vcf_args} \\
        -O ${sample_name}.dragen.merged.vcf.gz

    ${params.bcftools} index -t ${sample_name}.dragen.merged.vcf.gz
    """
}

process dragenHardFilter {
    tag "DRAGEN hard filter for ${sample_name}"
    publishDir "${params.dragen_vcf_output}/${sample_name}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '16.GB'
    cpus 2
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(raw_vcf), path(raw_tbi)

    output:
    tuple val(sample_name), path("${sample_name}.dragen.filtered.vcf.gz"), path("${sample_name}.dragen.filtered.vcf.gz.tbi")

    script:
    """
    set +u
    source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
    conda activate gatk4-*******
    set -u
    
    gatk VariantFiltration \\
        -V ${raw_vcf} \\
        --filter-expression "QUAL < 10.4139" \\
        --filter-name "DRAGENHardQUAL" \\
        -O ${sample_name}.dragen.filtered.vcf.gz
    """
}

// ====================================================================================
// MAIN WORKFLOW
// ====================================================================================
workflow {

    // Show help message if requested
    if (params.help) {
        helpMessage()
        exit(0)
    }

    // Validate required parameters
    if (!params.fastq_dir) {
        log.error("ERROR: --fastq_dir parameter is required")
        exit(1)
    }
    if (!params.dragmap_reference) {
        log.error("ERROR: --dragmap_reference parameter is required")
        exit(1)
    }
    if (!params.str_table_file) {
        log.error("ERROR: --str_table_file parameter is required")
        exit(1)
    }
    if (!params.interval_dir) {
        log.error("ERROR: --interval_dir parameter is required")
        exit(1)
    }

    // Step 1: Prepare input channels
    // Get all FASTQ files from WES folder
    wes_samples_ch = Channel.fromFilePairs("${params.fastq_dir}/**/*_{R1,R2}*.fastq.gz")
        .map { pair_id, files -> tuple(pair_id.split('_')[0], files) }

    // Get all interval files
    interval_files_ch = Channel.fromPath("${params.interval_dir}/*.bed")
        .map { file -> tuple(file.baseName, file) }

    // Step 2: Run DRAGMAP alignment
    dragmap_bams_ch = dragmapAlignment(wes_samples_ch)

    // Step 3: Calibrate DRAGSTR model for each sample
    dragstr_models_ch = calibrateDragstrModel(dragmap_bams_ch)


    // Step 4: Combine BAM and DRAGSTR model with intervals for parallel processing
    hc_input_ch = dragmap_bams_ch
        .join(dragstr_models_ch)
        .combine(interval_files_ch)
        .map { sample_name, bam, bai, dragstr_model, interval_name, interval_file ->
            tuple(sample_name, bam, bai, dragstr_model, interval_name, interval_file)
        }

    // Step 5: Run HaplotypeCaller in DRAGEN mode in parallel by intervals
    hc_results_ch = haplotypeCallerDragenModeParallel(hc_input_ch)

    // Step 6: Group results by sample and merge VCFs (sorted by part number)
    merged_input_ch = hc_results_ch
        .groupTuple()
        .map { sample_name, interval_names, vcfs, tbis ->
            // Create list of tuples with part numbers for sorting
            tuple(sample_name, vcfs.flatten(), tbis.flatten())
        }

    merged_vcfs_ch = mergeVCFs(merged_input_ch)

    // Step 7: Apply DRAGEN hard filtering
    filtered_vcfs_ch = dragenHardFilter(merged_vcfs_ch)

    // Display final results
    filtered_vcfs_ch.view { sample_name, vcf, _tbi ->
        "Final DRAGEN-filtered VCF for ${sample_name}: ${vcf}"
    }
}
