#!/usr/bin/env Rscript

# EdgeR Analysis of RSEM RNA-seq Data
# Author: Generated for RSEM differential expression analysis
# Date: 2025-01-23

# Load required libraries
suppressPackageStartupMessages({
    library(edgeR)
    library(limma)
    library(ggplot2)
    library(dplyr)
    library(readr)
    library(stringr)
    library(tidyverse)
    library(ComplexHeatmap)
    library(EnhancedVolcano)
    library(clusterProfiler)
    library(org.Hs.eg.db)
    library(DOSE)
    library(enrichplot)
    library(ggrepel)
    library(RColorBrewer)
    library(pheatmap)
    library(circlize)
})

# Set working directory and create output directories

dir.create("results", showWarnings = FALSE)
dir.create("results/plots", showWarnings = FALSE)
dir.create("results/tables", showWarnings = FALSE)

# Function to read RSEM results
read_rsem_results <- function(file_path) {
    data <- read_tsv(file_path, show_col_types = FALSE)
    return(data)
}

# Function to extract sample name from path
extract_sample_name <- function(path) {
    # Extract the folder name which should be the sample name
    sample_name <- basename(path) %>% str_remove("\\.genes\\.results$")
    return(sample_name)
}

# Find all RSEM gene results files
rsem_files <- list.files("rsem_counts",
    pattern = "\\.genes\\.results$",
    recursive = TRUE,
    full.names = TRUE
)

# Filter for Ade and Nm samples only
rsem_files <- rsem_files[grepl("(Ade|Nm)", rsem_files)]

cat("Found", length(rsem_files), "RSEM files:\n")
print(rsem_files)

# Read all RSEM files and create count matrix
sample_names <- sapply(rsem_files, extract_sample_name)
names(rsem_files) <- sample_names

# Read first file to get gene information
first_file <- read_rsem_results(rsem_files[1])
gene_info <- first_file[, c("gene_id", "transcript_id(s)", "length", "effective_length")]

# Create count matrix
count_data <- data.frame(gene_id = first_file$gene_id)

for (i in 1:length(rsem_files)) {
    sample_name <- names(rsem_files)[i]
    rsem_data <- read_rsem_results(rsem_files[i])

    # Use expected_count (rounded to integers for EdgeR)
    count_data[[sample_name]] <- round(rsem_data$expected_count)
}

# Set gene_id as rownames and remove gene_id column
rownames(count_data) <- count_data$gene_id
count_data$gene_id <- NULL

cat("Count matrix dimensions:", dim(count_data), "\n")
cat("Sample names:", colnames(count_data), "\n")

# Create sample information
sample_info <- data.frame(
    sample = colnames(count_data),
    group = ifelse(grepl("Ade", colnames(count_data)), "Adenoma", "Normal"),
    stringsAsFactors = FALSE
)

cat("Sample information:\n")
print(sample_info)

# Create DGEList object
dge <- DGEList(counts = count_data, group = sample_info$group)

# Filter low-expressed genes (keep genes with at least 1 CPM in at least 2 samples)
keep <- filterByExpr(dge, min.count = 10, min.total.count = 15)
dge <- dge[keep, , keep.lib.sizes = FALSE]

cat("After filtering:", nrow(dge), "genes retained\n")

# Normalize using TMM
dge <- calcNormFactors(dge, method = "TMM")

# Design matrix
design <- model.matrix(~ 0 + group, data = sample_info)
colnames(design) <- c("Adenoma", "Normal")

# Estimate dispersions
dge <- estimateDisp(dge, design)

# Fit GLM
fit <- glmQLFit(dge, design)

# Make contrast (Adenoma vs Normal)
contrast <- makeContrasts(Adenoma - Normal, levels = design)
qlf <- glmQLFTest(fit, contrast = contrast)

# Get results
results <- topTags(qlf, n = Inf)$table
results$gene_id <- rownames(results)

# Add gene symbols
results$symbol <- mapIds(org.Hs.eg.db,
    keys = str_remove(results$gene_id, "\\..*"),
    column = "SYMBOL",
    keytype = "ENSEMBL",
    multiVals = "first"
)

# Save results
write_csv(results, "results/tables/differential_expression_results.csv")

cat("Differential expression analysis completed\n")
cat("Significant genes (FDR < 0.05):", sum(results$FDR < 0.05, na.rm = TRUE), "\n")
cat("Upregulated in Adenoma:", sum(results$FDR < 0.05 & results$logFC > 0, na.rm = TRUE), "\n")
cat("Downregulated in Adenoma:", sum(results$FDR < 0.05 & results$logFC < 0, na.rm = TRUE), "\n")

# Calculate CPM for plotting
cpm_data <- cpm(dge, log = TRUE)

# PCA Analysis
pca_result <- prcomp(t(cpm_data), scale. = TRUE)
pca_data <- data.frame(
    PC1 = pca_result$x[, 1],
    PC2 = pca_result$x[, 2],
    sample = rownames(pca_result$x),
    group = sample_info$group
)

# Calculate variance explained
var_explained <- round(100 * summary(pca_result)$importance[2, 1:2], 1)

# PCA Plot
pca_plot <- ggplot(pca_data, aes(x = PC1, y = PC2, color = group)) +
    geom_point(size = 4, alpha = 0.8) +
    geom_text_repel(aes(label = sample), size = 3) +
    scale_color_manual(values = c("Adenoma" = "#E31A1C", "Normal" = "#1F78B4")) +
    labs(
        title = "PCA of RNA-seq Samples",
        x = paste0("PC1 (", var_explained[1], "% variance)"),
        y = paste0("PC2 (", var_explained[2], "% variance)"),
        color = "Group"
    ) +
    theme_bw() +
    theme(
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        legend.position = "bottom"
    )

ggsave("results/plots/pca_plot.png", pca_plot, width = 8, height = 6, dpi = 300)

cat("PCA plot saved\n")

# Volcano Plot using EnhancedVolcano
volcano_plot <- EnhancedVolcano(
    results,
    lab = results$symbol,
    x = "logFC",
    y = "FDR",
    title = "Adenoma vs Normal",
    subtitle = "Differential Expression Analysis",
    pCutoff = 0.05,
    FCcutoff = 1.0,
    pointSize = 2.0,
    labSize = 3.0,
    colAlpha = 0.7,
    legendPosition = "right",
    legendLabSize = 12,
    legendIconSize = 4.0,
    drawConnectors = TRUE,
    widthConnectors = 0.75,
    max.overlaps = 20
)

ggsave("results/plots/volcano_plot.png", volcano_plot, width = 12, height = 8, dpi = 300)

cat("Volcano plot saved\n")

# Heatmap of top differentially expressed genes
top_genes <- results[order(results$FDR), ][1:50, ]
heatmap_data <- cpm_data[rownames(cpm_data) %in% top_genes$gene_id, ]

# Add gene symbols to rownames
gene_symbols <- top_genes$symbol[match(rownames(heatmap_data), top_genes$gene_id)]
gene_symbols[is.na(gene_symbols)] <- str_remove(rownames(heatmap_data)[is.na(gene_symbols)], "\\..*")
rownames(heatmap_data) <- gene_symbols

# Calculate z-scores (row-wise standardization)
heatmap_data_zscore <- t(scale(t(heatmap_data)))

# Column annotation
col_annotation <- HeatmapAnnotation(
    Group = sample_info$group,
    col = list(Group = c("Adenoma" = "#E31A1C", "Normal" = "#1F78B4"))
)

# Create heatmap with z-score scaling
heatmap_plot <- Heatmap(
    heatmap_data_zscore,
    name = "Z-score",
    top_annotation = col_annotation,
    show_row_names = TRUE,
    show_column_names = TRUE,
    row_names_gp = gpar(fontsize = 8),
    column_names_gp = gpar(fontsize = 10),
    column_split = sample_info$group,
    clustering_distance_rows = "euclidean",
    clustering_distance_columns = "euclidean",
    col = colorRamp2(
        c(-2, 0, 2),
        c("blue", "white", "red")
    ),
    heatmap_legend_param = list(title = "Z-score")
)

png("results/plots/heatmap_top50_genes.png", width = 12, height = 10, units = "in", res = 300)
draw(heatmap_plot)
dev.off()

cat("Heatmap saved\n")

# GSEA Analysis
# Prepare gene list for GSEA - convert to Entrez IDs
ensembl_ids <- str_remove(results$gene_id, "\\..*") # Remove version numbers

# Convert ENSEMBL to Entrez IDs
entrez_ids <- mapIds(org.Hs.eg.db,
    keys = ensembl_ids,
    column = "ENTREZID",
    keytype = "ENSEMBL",
    multiVals = "first"
)

# Create gene list with Entrez IDs
gene_list <- results$logFC
names(gene_list) <- entrez_ids

# Remove genes without Entrez ID mapping and NA values
gene_list <- gene_list[!is.na(names(gene_list)) & !is.na(gene_list)]

# Handle duplicate Entrez IDs by keeping the one with highest absolute logFC
gene_df <- data.frame(
    entrez_id = names(gene_list),
    logFC = gene_list,
    abs_logFC = abs(gene_list),
    stringsAsFactors = FALSE
)

# For duplicates, keep the one with highest absolute logFC
gene_df <- gene_df %>%
    group_by(entrez_id) %>%
    slice_max(abs_logFC, n = 1, with_ties = FALSE) %>%
    ungroup()

# Recreate gene list without duplicates
gene_list <- gene_df$logFC
names(gene_list) <- gene_df$entrez_id
gene_list <- sort(gene_list, decreasing = TRUE)

cat("Performing GSEA analysis...\n")
cat("Genes with Entrez ID mapping:", length(gene_list), "\n")

# KEGG pathway analysis
kegg_gsea <- gseKEGG(
    geneList = gene_list,
    organism = "hsa",
    pvalueCutoff = 0.05,
    pAdjustMethod = "BH"
)

# GO Biological Process analysis
go_gsea <- gseGO(
    geneList = gene_list,
    OrgDb = org.Hs.eg.db,
    ont = "BP",
    keyType = "ENTREZID",
    pvalueCutoff = 0.05,
    pAdjustMethod = "BH"
)

# Save GSEA results
if (!is.null(kegg_gsea) && nrow(kegg_gsea@result) > 0) {
    write_csv(kegg_gsea@result, "results/tables/kegg_gsea_results.csv")
    cat("KEGG GSEA results saved\n")
} else {
    cat("No significant KEGG pathways found\n")
}

if (!is.null(go_gsea) && nrow(go_gsea@result) > 0) {
    write_csv(go_gsea@result, "results/tables/go_gsea_results.csv")
    cat("GO GSEA results saved\n")
} else {
    cat("No significant GO pathways found\n")
}

# Create GSEA dotplots
create_gsea_dotplot <- function(gsea_result, title, filename, max_pathways = 20) {
    if (is.null(gsea_result) || nrow(gsea_result@result) == 0) {
        cat("No significant pathways for", title, "\n")
        return(NULL)
    }

    # Separate activated and deactivated pathways
    activated <- gsea_result@result[gsea_result@result$NES > 0, ]
    deactivated <- gsea_result@result[gsea_result@result$NES < 0, ]

    # Limit to top pathways
    if (nrow(activated) > max_pathways) {
        activated <- activated[1:max_pathways, ]
    }
    if (nrow(deactivated) > max_pathways) {
        deactivated <- deactivated[1:max_pathways, ]
    }

    # Combine and create plot
    plot_data <- rbind(activated, deactivated)

    if (nrow(plot_data) == 0) {
        cat("No pathways to plot for", title, "\n")
        return(NULL)
    }

    plot_data$Direction <- ifelse(plot_data$NES > 0, "Activated", "Deactivated")
    plot_data$Description <- str_wrap(plot_data$Description, width = 50)

    p <- ggplot(plot_data, aes(x = NES, y = reorder(Description, NES))) +
        geom_point(aes(size = setSize, color = p.adjust), alpha = 0.8) +
        scale_color_gradient(low = "red", high = "blue", name = "Adj. P-value") +
        scale_size_continuous(name = "Gene Set Size", range = c(2, 8)) +
        facet_wrap(~Direction, scales = "free_y", ncol = 2) +
        labs(
            title = paste("GSEA:", title),
            x = "Normalized Enrichment Score (NES)",
            y = "Pathway"
        ) +
        theme_bw() +
        theme(
            plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
            axis.text.y = element_text(size = 8),
            strip.text = element_text(size = 12, face = "bold")
        )

    ggsave(filename, p, width = 8, height = 6, dpi = 300)
    cat("GSEA dotplot saved:", filename, "\n")

    return(p)
}

# Create KEGG dotplot
if (!is.null(kegg_gsea) && nrow(kegg_gsea@result) > 0) {
    create_gsea_dotplot(kegg_gsea, "KEGG Pathways", "results/plots/kegg_gsea_dotplot.png")
}

# Create GO BP dotplot
if (!is.null(go_gsea) && nrow(go_gsea@result) > 0) {
    create_gsea_dotplot(go_gsea, "GO Biological Processes", "results/plots/go_gsea_dotplot.png")
}

# Summary report
cat("\n=== EdgeR Analysis Summary ===\n")
cat("Total samples analyzed:", ncol(count_data), "\n")
cat("Adenoma samples:", sum(sample_info$group == "Adenoma"), "\n")
cat("Normal samples:", sum(sample_info$group == "Normal"), "\n")
cat("Genes after filtering:", nrow(dge), "\n")
cat("Significant DE genes (FDR < 0.05):", sum(results$FDR < 0.05, na.rm = TRUE), "\n")
cat("Upregulated in Adenoma:", sum(results$FDR < 0.05 & results$logFC > 0, na.rm = TRUE), "\n")
cat("Downregulated in Adenoma:", sum(results$FDR < 0.05 & results$logFC < 0, na.rm = TRUE), "\n")

if (!is.null(kegg_gsea) && nrow(kegg_gsea@result) > 0) {
    cat("Significant KEGG pathways:", nrow(kegg_gsea@result), "\n")
}

if (!is.null(go_gsea) && nrow(go_gsea@result) > 0) {
    cat("Significant GO BP pathways:", nrow(go_gsea@result), "\n")
}

cat("\nAll results saved in 'results/' directory\n")
cat("Plots saved in 'results/plots/'\n")
cat("Tables saved in 'results/tables/'\n")

cat("\nAnalysis completed successfully!\n")
