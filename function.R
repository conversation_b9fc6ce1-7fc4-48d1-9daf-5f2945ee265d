# VCF Processing Functions
# Collection of utility functions for VCF file processing and analysis

# Load required packages
library(vcfR)
library(tidyverse)

#' Parse VEP-annotated VCF file to tibble
#'
#' This function reads a VEP-annotated VCF file and converts it to a tibble
#' by parsing the CSQ (Consequence) field and combining it with variant
#' and genotype information.
#'
#' @param vcf_path Character string specifying the path to the VCF file
#' @return A tibble containing variant information with VEP annotations
#' @examples
#' \dontrun{
#'   # Parse a single VCF file
#'   variants <- parse_vcf_csq("sample.annotated.vep.vcf")
#'   
#'   # Parse multiple VCF files
#'   vcf_files <- list.files("vcf_dir", pattern = "\\.vcf$", full.names = TRUE)
#'   all_variants <- map_dfr(vcf_files, parse_vcf_csq)
#' }
#' @export
parse_vcf_csq <- function(vcf_path) {
  # Read the VCF file
  vcf <- read.vcfR(vcf_path, verbose = FALSE)

  # --- MODIFIED SECTION ---
  # Directly parse the vcf@meta slot to find the CSQ description
  csq_line <- grep("ID=CSQ", vcf@meta, value = TRUE)

  if (length(csq_line) == 0) {
    stop("CSQ field description not found in VCF header.")
  }

  # Extract the field names from the 'Format: ...' part of the description
  csq_fields <- str_split(
    str_remove(
      # Grab the string that starts with "Format: " and ends before the closing quote
      str_extract(csq_line, "Format:[^\\\"]+"), "Format:"
    ),
    "\\|"
  )[[1]]
  # --- END MODIFIED SECTION ---

  # Extract CSQ data and variant identifiers
  csq_data <- extract.info(vcf, element = "CSQ", as.numeric = FALSE)
  
  # Combine VCF components and parse CSQ field
  bind_cols(
    vcf@fix,
    vcf@gt,
    INFO2df(vcf)
  ) %>%
    separate(CSQ, csq_fields, sep = "\\|")
}
