# Load required packages and functions
library(gt)
library(tidyverse)

# Source the VCF parsing function
source("function.R")

germline <- parse_vcf_csq("./germline_vcf/germline.annotated.vep.vcf")




four_genes <- germline %>%
    dplyr::filter(
        SYMBOL %in% c("MLH1", "MSH2", "MSH6", "PMS2"),
        IMPACT %in% c("MODERATE", "HIGH")
    ) %>%
    mutate(across(starts_with("S2"),
        list(
            gt = ~ map_chr(., ~ str_split_1(., ":")[1]),
            cnt = ~ map_chr(., ~ str_split_1(., ":")[2])
        ),
        .names = "{.col}_{.fn}"
    )) %>%
    select(variant_id, SYMBOL, HGVSc, HGVSp, IMPACT, Consequence, Existing_variation, starts_with("S2"))
library(knitr)


four_genes %>%
    select(variant_id, SYMBOL, HGVSp, Consequence, Existing_variation, starts_with("S22-E1-Nm_"), starts_with("S24-B2-Nm_")) %>%
    mutate(HGVSp = map_chr(HGVSp, ~ str_split_1(., "p.")[2])) %>%
    dplyr::filter(`S22-E1-Nm_gt` != "0/0") %>%
    unite(HGVsp, SYMBOL, HGVSp, sep = ".") %>%
    mutate(
        clinVar = c(
            "Hereditary cancer-predisposing syndrome|Likely-Pathogenic",
            c("Lynch syndrome|Benign"),
            c("Lynch syndrome|Benign")
        ),
        GnomAD_genomes_freq = c(0.000003, 0.22, 0.15)
    ) %>%
    gt()


four_genes %>%
    select(variant_id, SYMBOL, HGVSp, Consequence, Existing_variation, starts_with("S23-B1-Nm_")) %>%
    mutate(HGVSp = map_chr(HGVSp, ~ str_split_1(., "p.")[2])) %>%
    dplyr::filter(`S23-B1-Nm_gt` != "0/0") %>%
    unite(HGVsp, SYMBOL, HGVSp, sep = ".") %>%
    mutate(
        clinVar = c(
            "Lynch syndrome|Pathogenic",
            c("Lynch syndrome|Benign"),
            c("Lynch syndrome|Benign")
        ),
        GnomAD_genomes_freq = c(NA, 0.15, 0.40)
    ) %>%
    gt()
