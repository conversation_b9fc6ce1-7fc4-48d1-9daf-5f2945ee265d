library(tidyverse)


meta <- read_csv("meta.csv")

norm <- meta %>%
    dplyr::filter(is.na(`Adenoma Type`)) %>%
    drop_na(`Pt ID`) %>%
    distinct(`Pt ID`, Sample_Name) %>%
    rename(Paired_Normal_WEX = Sample_Name)



lesion <- meta %>%
    dplyr::filter(!is.na(`Adenoma Type`)) %>%
    distinct(`Pt ID`, Sample_Name, Collection, `Adenoma Type`, Tumor_RNAseq) %>%
    drop_na(Sample_Name) %>%
    rename(Tumor_WEX = Sample_Name) %>%
    mutate(
        type = case_match(
            `Adenoma Type`,
            c("Tubular  Adenoma", "Tubular Adenoma") ~ "TA",
            "Tubulovillous Adenoma" ~ "TVA",
            "Sessile Serrated" ~ "SSA",
            "Serrated polyp" ~ "SP"
        )
    ) %>%
    mutate(idx = row_number(), .by = c(`Pt ID`, Collection, type))



norm %>%
    full_join(lesion) %>%
    mutate(Sample = str_c(`Pt ID`, "_", Collection, "_", type, "_", idx)) %>%
    arrange(`Pt ID`, Tumor_WEX, Paired_Normal_WEX) %>%
    select(Sample, Tumor_WEX, Paired_Normal_WEX, Tumor_RNAseq) %>%
    write_csv("meta_wide.csv")
