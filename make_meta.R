library(tidyverse)


meta <- read_csv("meta.csv")

norm <- meta %>%
    dplyr::filter(is.na(`Adenoma Type`)) %>%
    drop_na(`Pt ID`) %>%
    distinct(`Pt ID`, `ATGC name`) %>%
    rename(Paired_Normal_WEX = `ATGC name`)



lesion <- meta %>%
    dplyr::filter(!is.na(`Adenoma Type`)) %>%
    distinct(`Pt ID`, `ATGC name`, Collection, `Adenoma Type`, Tumor_RNAseq) %>%
    drop_na(`ATGC name`) %>%
    rename(Tumor_WEX = `ATGC name`) %>%
    mutate(
        type = case_match(
            `Adenoma Type`,
            c("Tubular  Adenoma", "Tubular Adenoma") ~ "TA",
            "Tubulovillous Adenoma" ~ "TVA",
            "Sessile Serrated" ~ "SSA",
            "Serrated polyp" ~ "SP"
        )
    ) %>%
    mutate(idx = row_number(), .by = c(`Pt ID`, Collection, type))



norm %>%
    full_join(lesion) %>%
    mutate(Sample = str_c(`Pt ID`, "_", Collection, "_", type, "_", idx)) %>%
    arrange(`Pt ID`, Tumor_WEX, Paired_Normal_WEX) %>%
    select(Sample, Tumor_WEX, Paired_Normal_WEX, Tumor_RNAseq) %>%
    write_csv("meta_wide.csv")
