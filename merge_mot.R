library(tidyverse)

# Parse command line arguments
args <- commandArgs(trailingOnly = TRUE)

# Find input and output file arguments
input_idx <- which(args == "-I")
output_idx <- which(args == "-O")

if (length(input_idx) == 0 || length(output_idx) == 0) {
  stop("Usage: Rscript script.R -I input1.txt input2.txt ... -O output.txt")
}

# Extract input files (everything between -I and -O)
input_files <- args[(input_idx + 1):(output_idx - 1)]
output_file <- args[output_idx + 1]

if (length(input_files) == 0) {
  stop("No input files specified")
}

read_mot <- function(file) {
  read_delim(file, delim = ":", col_names = c("Chr", "Start", "End", "Motif", "Count")) %>%
    dplyr::filter(str_detect(Chr, "^chr([0-9]{1,2}|X|Y)$")) %>%
    mutate(
      Count = str_split(Count, ", "),
      Ref_count = map_int(Count, ~as.numeric(.[1])),
      Count = map(Count, ~.[2:length(.)])
    ) %>%
    unnest(Count) %>%
    mutate(
      Name = rep(c("Count", "Hist"), n() / 2),
      index = ceiling(row_number() / 2)
    ) %>%
    pivot_wider(
      names_from = Name,
      values_from = Count,
      id_cols = c("Chr", "Start", "End", "Motif", "Ref_count", "index")
    ) %>%
    select(-index) %>%
    summarise(
      Hist = sum(as.numeric(Hist), na.rm = TRUE),
      .by = c("Chr", "Start", "End", "Motif", "Ref_count", "Count")
    )
}

bind_mot_tf <- function(x, y) {
  full_join(x, y, by = c("Chr", "Start", "End", "Motif", "Ref_count", "Count")) %>%
    mutate(Hist = Hist.x + Hist.y) %>%
    select(-Hist.x, -Hist.y)
}

# Process input files
if (length(input_files) == 1) {
  x <- read_mot(input_files)
} else {
  x <- map(input_files, read_mot) %>%
    reduce(bind_mot_tf)
}

# Format and write output
x %>%
  unite(X2, Count, Hist, sep = ", ") %>%
  summarise(
    X2 = paste(X2, collapse = ", "),
    .by = c(Chr, Start, End, Motif, Ref_count)
  ) %>%
  arrange(factor(Chr, levels = c(paste0("chr", 1:99), "chrX", "chrY")), Start) %>%
  unite(X1, Chr, Start, End, Motif, Ref_count, sep = ":") %>%
  unite(X1, X1, X2, sep = ", ") %>%
  write_tsv(output_file, col_names = FALSE)
