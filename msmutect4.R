library(tidyverse)
library(ggpubr)

# Source the VCF parsing function
source("function.R")

vcf <- dir("./final_somatic_vcfs", recursive = T, full.names = T) %>%
    str_subset("vep") %>%
    map_dfr(
        ~ parse_vcf_csq(.x) %>%
            mutate(sample = basename(.x) %>% str_split_1("\\.") %>% `[`(1))
    )


vcf %>%
    mutate(CALLER = CALLER %>% str_replace("msmutect", "MsMutect2")) %>%
    group_by(CHROM, POS, ALT, sample) %>%
    summarise(CALLER = CALLER %>% unique() %>% sort() %>% str_c(collapse = ","), .groups = "drop") %>%
    count(sample, CALLER) %>%
    group_by(sample) %>%
    mutate(prop = n / sum(n)) %>%
    ggbarplot(
        x = "sample",
        y = "prop",
        fill = "CALLER"
    )


vcf %>%
    mutate(CALLER = CALLER %>% str_replace("msmutect", "MsMutect2")) %>%
    group_by(CHROM, POS, sample) %>%
    summarise(CALLER = CALLER %>% unique() %>% sort() %>% str_c(collapse = ","), .groups = "drop") %>%
    count(sample, CALLER) %>%
    group_by(sample) %>%
    mutate(prop = n / sum(n)) %>%
    ggbarplot(
        x = "sample",
        y = "prop",
        fill = "CALLER"
    )

vcf %>%
    mutate(CALLER = CALLER %>% str_replace("msmutect", "MsMutect2")) %>%
    distinct(CHROM, POS, ALT, sample, CALLER) %>%
    count(sample, CALLER) %>%
    ggbarplot(
        x = "sample",
        y = "n",
        fill = "CALLER",
        position = position_dodge2()
    )




list.files("./MSIsensor", full.names = T) %>%
    map_df(
        ~ read_tsv(.x) %>%
            mutate(sample = basename(.x))
    ) %>%
    arrange(sample) %>%
    gt()

msi <- list.files("./MSIsensor", full.names = T) %>%
    map_df(
        ~ read_tsv(.x) %>%
            mutate(sample = basename(.x))
    )


ms4 <- list.files("./msmutect", full.names = T, pattern = "vcf") %>%
    map_df(
        ~ read_tsv(.x, comment = "##") %>%
            mutate(sample = basename(.x) %>% str_remove(".vcf"))
    )

ms4 %>%
    count(FILTER, sample) %>%
    mutate(
        prop = n / sum(n),
        .by = sample
    ) %>%
    ggbarplot(
        x = "sample",
        y = "prop",
        fill = "FILTER"
    )

library(ComplexHeatmap)

target_I1 <- ms4 %>%
    dplyr::filter(FILTER == "PASS", sample == "PT_103687_I1_Ade_BL")


ms4 %>%
    pivot_wider(
        names_from = sample,
        values_from = FILTER
    ) %>%
    select(starts_with("PT")) %>%
    slice_sample(n = 200) %>%
    Heatmap()



ms4 %>%
    semi_join(
        target_I1,
        by = c("#CHROM", "POS")
    ) %>%
    complete(`POS`, sample, fill = list(FILTER = NA)) %>%
    count(sample, FILTER) %>%
    mutate(prop = n / sum(n), .by = sample) %>%
    ggbarplot(
        x = "sample",
        fill = "FILTER",
        y = "prop"
    )


count(FILTER, sample) %>%
    mutate(
        prop = n / sum(n),
        .by = sample
    ) %>%
    ggbarplot(
        x = "sample",
        y = "prop",
        fill = "FILTER"
    )
ms4 %>%
    dplyr::filter(
        FILTER %in% c("PASS", "INS", "RR", "FFT", "NM")
    ) %>%
    count(FILTER, sample) %>%
    mutate(
        prop = n / sum(n),
        .by = sample
    ) %>%
    ggbarplot(
        x = "sample",
        y = "prop",
        fill = "FILTER"
    )


ms4 %>%
    dplyr::filter(
        FILTER %in% c("PASS", "INS", "RR", "FFT", "NM")
    ) %>%
    count(FILTER, sample) %>%
    mutate(
        prop = n / sum(n),
        .by = sample
    ) %>%
    dplyr::filter(FILTER == "PASS") %>%
    left_join(msi %>% rename(MSIscore = `%`),
        by = "sample"
    ) %>%
    ggscatter(
        x = "prop",
        y = "MSIscore",
        xlab = "PASS ratio in Callable "
    )



vcf %>%
    dplyr::filter(IMPACT %in% c("MODERATE", "HIGH")) %>%
    mutate(type = case_when(str_length(ALT) == str_length(REF) ~ "SNV", T ~ "INDEL")) %>%
    dplyr::filter(type == "SNV") %>%
    distinct(
        type, CHROM, POS, Consequence, sample
    ) %>%
    unite(id, CHROM, POS) %>%
    group_by(id) %>%
    dplyr::filter(n_distinct(sample) >= 2) %>%
    pivot_wider(
        id_cols = id,
        names_from = sample,
        values_from = Consequence,
        values_fn = ~ .x %>%
            unique() %>%
            sort() %>%
            str_c(collapse = ",")
    ) %>%
    column_to_rownames("id") %>%
    Heatmap()




vcf %>%
    dplyr::filter(IMPACT %in% c("MODERATE", "HIGH")) %>%
    mutate(type = case_when(str_length(ALT) == str_length(REF) ~ "SNV", T ~ "INDEL")) %>%
    dplyr::filter(type != "SNV") %>%
    distinct(
        type, CHROM, POS, Consequence, sample
    ) %>%
    unite(id, CHROM, POS) %>%
    group_by(id) %>%
    dplyr::filter(n_distinct(sample) >= 2) %>%
    pivot_wider(
        id_cols = id,
        names_from = sample,
        values_from = Consequence,
        values_fn = ~ .x %>%
            unique() %>%
            sort() %>%
            str_c(collapse = ",")
    ) %>%
    column_to_rownames("id") %>%
    Heatmap()


library(tibble)

# Class I
hla_typing <- tibble::tibble(
    Group = paste0("Group", 1:5),

    # Class I (some overlaps)
    HLA_A1 = c("HLA-A*02:01", "HLA-A*02:01", "HLA-A*23:01", "HLA-A*01:01", "HLA-A*01:01"),
    HLA_A2 = c("HLA-A*24:02", "HLA-A*03:01", "HLA-A*68:01", "HLA-A*24:02", "HLA-A*30:01"),
    HLA_B1 = c("HLA-B*07:02", "HLA-B*08:01", "HLA-B*07:02", "HLA-B*51:01", "HLA-B*40:01"),
    HLA_B2 = c("HLA-B*35:01", "HLA-B*35:01", "HLA-B*18:01", "HLA-B*07:02", "HLA-B*44:02"),
    HLA_C1 = c("HLA-C*04:01", "HLA-C*05:01", "HLA-C*04:01", "HLA-C*01:02", "HLA-C*03:04"),
    HLA_C2 = c("HLA-C*07:02", "HLA-C*07:02", "HLA-C*12:03", "HLA-C*15:02", "HLA-C*07:02"),

    # Class II (more overlaps)
    HLA_DRB1_1 = c("HLA-DRB1*15:01", "HLA-DRB1*15:01", "HLA-DRB1*13:02", "HLA-DRB1*01:01", "HLA-DRB1*01:01"),
    HLA_DRB1_2 = c("HLA-DRB1*03:01", "HLA-DRB1*11:01", "HLA-DRB1*03:01", "HLA-DRB1*12:01", "HLA-DRB1*14:01"),
    HLA_DQB1_1 = c("HLA-DQB1*06:02", "HLA-DQB1*03:02", "HLA-DQB1*06:02", "HLA-DQB1*05:01", "HLA-DQB1*02:02"),
    HLA_DQB1_2 = c("HLA-DQB1*02:01", "HLA-DQB1*06:03", "HLA-DQB1*02:01", "HLA-DQB1*04:02", "HLA-DQB1*05:03"),
    HLA_DPB1_1 = c("HLA-DPB1*04:01", "HLA-DPB1*02:01", "HLA-DPB1*04:01", "HLA-DPB1*17:01", "HLA-DPB1*05:01"),
    HLA_DPB1_2 = c("HLA-DPB1*01:01", "HLA-DPB1*03:01", "HLA-DPB1*01:01", "HLA-DPB1*13:01", "HLA-DPB1*09:01")
) %>% pivot_longer(-1)


hla_typing %>%
    mutate(
        name = name %>% str_remove("(1|2)$"),
        Group = Group %>% str_replace("Group", "Sample"),
        result = T
    ) %>%
    pivot_wider(
        id_cols = Group,
        names_from = value,
        values_from = result
    ) %>%
    column_to_rownames("Group") %>%
    Heatmap()






vcf %>%
    dplyr::filter(IMPACT %in% c("MODERATE", "HIGH")) %>%
    mutate(type = case_when(str_length(ALT) == str_length(REF) ~ "SNV", T ~ "INDEL")) %>%
    dplyr::filter(type == "SNV") %>%
    distinct(
        type, SYMBOL, Consequence, sample
    ) %>%
    group_by(SYMBOL) %>%
    dplyr::filter(n_distinct(sample) >= 2) %>%
    pivot_wider(
        id_cols = SYMBOL,
        names_from = sample,
        values_from = Consequence,
        values_fn = ~ .x %>%
            unique() %>%
            sort() %>%
            str_c(collapse = ",")
    ) %>%
    ungroup() %>%
    slice_sample(n = 25) %>%
    dplyr::filter(SYMBOL != "") %>%
    column_to_rownames("SYMBOL") %>%
    Heatmap()


vcf %>%
    dplyr::filter(IMPACT %in% c("MODERATE", "HIGH")) %>%
    mutate(type = case_when(str_length(ALT) == str_length(REF) ~ "SNV", T ~ "INDEL")) %>%
    dplyr::filter(type != "SNV") %>%
    distinct(
        type, SYMBOL, Consequence, sample
    ) %>%
    group_by(SYMBOL) %>%
    dplyr::filter(n_distinct(sample) >= 2) %>%
    pivot_wider(
        id_cols = SYMBOL,
        names_from = sample,
        values_from = Consequence,
        values_fn = ~ .x %>%
            unique() %>%
            sort() %>%
            str_c(collapse = ",")
    ) %>%
    ungroup() %>%
    slice_sample(n = 45) %>%
    dplyr::filter(SYMBOL != "") %>%
    column_to_rownames("SYMBOL") %>%
    Heatmap()
