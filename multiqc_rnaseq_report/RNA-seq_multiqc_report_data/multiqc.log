[2025-06-30 11:41:36,699] root                                               [DEBUG  ]  Logging to file: /tmp/tmp6z4i86ft/multiqc.log
[2025-06-30 11:41:36,700] multiqc.core.update_config                         [DEBUG  ]  This is MultiQC v1.29
[2025-06-30 11:41:36,701] multiqc.core.update_config                         [DEBUG  ]  Running Python 3.12.4 | packaged by conda-forge | (main, Jun 17 2024, 10:23:07) [GCC 12.3.0]
[2025-06-30 11:41:36,703] urllib3.connectionpool                             [DEBUG  ]  Starting new HTTPS connection (1): api.multiqc.info:443
[2025-06-30 11:41:38,837] multiqc.core.version_check                         [DEBUG  ]  Timed out after waiting for 2s for multiqc.info to check latest version: HTTPSConnectionPool(host='api.multiqc.info', port=443): Max retries exceeded with url: /version?version_multiqc=1.29&version_python=3.12.4&operating_system=Linux&is_docker=False&is_singularity=False&is_conda=True&is_ci=False&is_notebook=False&ai_summary=False&ai_summary_full=False&ai_provider=seqera (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x155540aaac00>, 'Connection to api.multiqc.info timed out. (connect timeout=1.0)'))
[2025-06-30 11:41:38,838] multiqc.multiqc                                    [DEBUG  ]  Working dir : /rsrch3/scratch/ccp-rsch/ndeng1/nouscom_NeoAg/work/a1/4359929323d2951afba75cd8ad2846
[2025-06-30 11:41:38,838] multiqc.multiqc                                    [DEBUG  ]  Template    : default
[2025-06-30 11:41:38,838] multiqc.multiqc                                    [DEBUG  ]  Command used: /rsrch3/home/<USER>/ndeng1/.local/bin/multiqc . -n RNA-seq_multiqc_report
[2025-06-30 11:41:38,838] multiqc.core.file_search                           [INFO   ]  Search path: /rsrch3/scratch/ccp-rsch/ndeng1/nouscom_NeoAg/work/a1/4359929323d2951afba75cd8ad2846
[2025-06-30 11:41:38,838] multiqc.core.file_search                           [DEBUG  ]  Analysing modules: bases2fastq, cellranger_arc, cells2stats, custom_content, ccs, ngsderive, purple, conpair, isoseq, lima, peddy, percolator, haplocheck, somalier, methylqa, mosdepth, phantompeakqualtools, qualimap, bamdst, preseq, hifiasm, quast, qorts, rna_seqc, rockhopper, rsem, rseqc, busco, checkm, bustools, goleft_indexcov, gffcompare, disambiguate, supernova, deeptools, sargasso, verifybamid, mirtrace, happy, mirtop, glimpse, gopeaks, homer, hops, macs2, theta2, snpeff, gatk, htseq, bcftools, featurecounts, fgbio, dragen, dragen_fastqc, dedup, pbmarkdup, damageprofiler, mapdamage, biobambam2, jcvi, mtnucratio, picard, vep, bakta, prokka, checkm2, qc3C, nanoq, nanostat, samblaster, samtools, bamtools, sambamba, ngsbits, pairtools, sexdeterrmine, seqera_cli, eigenstratdatabasetools, jellyfish, vcftools, longranger, stacks, varscan2, snippy, umicollapse, umitools, truvari, megahit, ganon, gtdbtk, bbmap, bismark, biscuit, diamond, hicexplorer, hicup, hicpro, salmon, kallisto, slamdunk, star, hisat2, tophat, bowtie2, bowtie1, hostile, cellranger, snpsplit, odgi, vg, pangolin, nextclade, freyja, humid, kat, leehom, librarian, nonpareil, adapterremoval, bbduk, clipandmerge, cutadapt, flexbar, sourmash, kaiju, kraken, malt, motus, trimmomatic, sickle, skewer, sortmerna, biobloomtools, fastq_screen, afterqc, fastp, fastqc, sequali, filtlong, prinseqplusplus, pychopper, porechop, pycoqc, minionqc, anglerfish, multivcfanalyzer, clusterflow, checkqc, bcl2fastq, bclconvert, interop, ivar, flash, seqyclean, optitype, whatshap, spaceranger, xenome, xengsort, metaphlan, seqwho, telseq, ataqv, mgikit, mosaicatcher, software_versions
[2025-06-30 11:41:38,838] multiqc.core.file_search                           [DEBUG  ]  Search keys: adapterremoval, afterqc, anglerfish, ataqv, bakta, bamdst, bamtools, bases2fastq, bbduk, bbmap, bcftools, bcl2fastq, bclconvert, biobambam2, biobloomtools, biscuit, bismark, bowtie1, bowtie2, busco, bustools, ccs, cellranger, cellranger_arc, cells2stats, checkm, checkm2, checkqc, clipandmerge, clusterflow, conpair, custom_content, cutadapt, damageprofiler, dedup, deeptools, diamond, disambiguate, dragen, dragen_fastqc, eigenstratdatabasetools, fastp, fastq_screen, fastqc, featurecounts, fgbio, filtlong, flash, flexbar, freyja, ganon, gatk, gffcompare, glimpse, goleft_indexcov, gopeaks, gtdbtk, haplocheck, happy, hicexplorer, hicpro, hicup, hifiasm, hisat2, homer, hops, hostile, htseq, humid, interop, isoseq, ivar, jcvi, jellyfish, kaiju, kallisto, kat, kraken, leehom, librarian, lima, longranger, macs2, malt, mapdamage, megahit, metaphlan, methylqa, mgikit, minionqc, mirtop, mirtrace, mosaicatcher, mosdepth, motus, mtnucratio, multiqc_data, multivcfanalyzer, nanoq, nanostat, nextclade, ngsbits, ngsderive, nonpareil, odgi, optitype, pairtools, pangolin, pbmarkdup, peddy, percolator, phantompeakqualtools, picard, porechop, preseq, prinseqplusplus, prokka, purple, pychopper, pycoqc, qc3C, qorts, qualimap, quast, rna_seqc, rockhopper, rsem, rseqc, salmon, sambamba, samblaster, samtools, sargasso, seqera_cli, sequali, seqwho, seqyclean, sexdeterrmine, sickle, skewer, slamdunk, snippy, snpeff, snpsplit, software_versions, somalier, sortmerna, sourmash, spaceranger, stacks, star, supernova, telseq, theta2, tophat, trimmomatic, truvari, umicollapse, umitools, varscan2, vcftools, vep, verifybamid, vg, whatshap, xengsort, xenome
[2025-06-30 11:41:40,860] multiqc.report                                     [DEBUG  ]  Summary of files that were skipped by the search: |skipped_no_match: 30|, |skipped_module_specific_max_filesize: 25|
[2025-06-30 11:41:40,861] multiqc.core.exec_modules                          [DEBUG  ]  Running module: custom_content
[2025-06-30 11:41:40,864] multiqc.core.exec_modules                          [DEBUG  ]  No samples found: custom_content
[2025-06-30 11:41:40,864] multiqc.core.exec_modules                          [DEBUG  ]  Running module: fastqc
[2025-06-30 11:41:41,060] multiqc.modules.fastqc.fastqc                      [INFO   ]  Found 24 reports
[2025-06-30 11:41:41,472] multiqc.report                                     [DEBUG  ]  Wrote data file multiqc_fastqc.txt
[2025-06-30 11:41:41,473] multiqc.report                                     [DEBUG  ]  Wrote data file multiqc_fastqc.json
[2025-06-30 11:41:41,475] multiqc.base_module                                [DEBUG  ]  fastqc: deleting attribute self.order_of_duplication_levels
[2025-06-30 11:41:41,475] multiqc.base_module                                [DEBUG  ]  fastqc: deleting attribute self.status_colours
[2025-06-30 11:41:41,475] multiqc.core.software_versions                     [DEBUG  ]  Reading software versions from config.software_versions
[2025-06-30 11:41:41,478] multiqc.report                                     [DEBUG  ]  Wrote data file multiqc_software_versions.txt
[2025-06-30 11:41:41,478] multiqc.core.write_results                         [DEBUG  ]  Rendering plots
[2025-06-30 11:41:41,607] multiqc.report                                     [DEBUG  ]  Wrote data file multiqc_general_stats.txt
[2025-06-30 11:41:41,607] multiqc.report                                     [DEBUG  ]  Wrote data file multiqc_general_stats.json
[2025-06-30 11:41:41,613] multiqc.core.write_results                         [DEBUG  ]  Exporting plot data to files
[2025-06-30 11:41:41,613] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_sequence_counts_plot.txt
[2025-06-30 11:41:41,615] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_per_base_sequence_quality_plot.txt
[2025-06-30 11:41:41,617] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_per_sequence_quality_scores_plot.txt
[2025-06-30 11:41:41,621] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_per_sequence_gc_content_plot_Percentages.txt
[2025-06-30 11:41:41,624] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_per_sequence_gc_content_plot_Counts.txt
[2025-06-30 11:41:41,626] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_per_base_n_content_plot.txt
[2025-06-30 11:41:41,627] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_sequence_duplication_levels_plot.txt
[2025-06-30 11:41:41,627] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_overrepresented_sequences_plot.txt
[2025-06-30 11:41:41,627] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_top_overrepresented_sequences_table.txt
[2025-06-30 11:41:41,629] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc_adapter_content_plot.txt
[2025-06-30 11:41:41,630] multiqc.report                                     [DEBUG  ]  Wrote data file fastqc-status-check-heatmap.txt
[2025-06-30 11:41:41,630] multiqc.core.write_results                         [DEBUG  ]  Moving data file from '/tmp/tmp6z4i86ft/multiqc_data' to '/rsrch3/scratch/ccp-rsch/ndeng1/nouscom_NeoAg/work/a1/4359929323d2951afba75cd8ad2846/RNA-seq_multiqc_report_data'
[2025-06-30 11:41:41,770] multiqc.core.write_results                         [INFO   ]  Data        : RNA-seq_multiqc_report_data
[2025-06-30 11:41:41,945] multiqc.core.write_results                         [DEBUG  ]  Compressing plot data
[2025-06-30 11:41:42,116] multiqc.core.write_results                         [INFO   ]  Report      : RNA-seq_multiqc_report.html
[2025-06-30 11:41:42,116] multiqc.core.write_results                         [DEBUG  ]  Report HTML written to /rsrch3/scratch/ccp-rsch/ndeng1/nouscom_NeoAg/work/a1/4359929323d2951afba75cd8ad2846/RNA-seq_multiqc_report.html
