Sample	Filename	File type	Encoding	Total Sequences	Sequences flagged as poor quality	Sequence length	%GC	total_deduplicated_percentage	avg_sequence_length	median_sequence_length	basic_statistics	per_base_sequence_quality	per_tile_sequence_quality	per_sequence_quality_scores	per_base_sequence_content	per_sequence_gc_content	per_base_n_content	sequence_length_distribution	sequence_duplication_levels	overrepresented_sequences	adapter_content
S22-E1-Ade_S11_L001_R1_001	S22-E1-Ade_S11_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	48023120.0	0.0	101.0	54.0	24.188887806497977	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S22-E1-Ade_S11_L001_R2_001	S22-E1-Ade_S11_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	48023120.0	0.0	101.0	53.0	12.006537125419285	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S22-E1-Nm_S12_L001_R1_001	S22-E1-Nm_S12_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	49730145.0	0.0	101.0	52.0	24.07943006917926	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S22-E1-Nm_S12_L001_R2_001	S22-E1-Nm_S12_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	49730145.0	0.0	101.0	51.0	11.226421075254907	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S22-F1-Ade_S10_L001_R1_001	S22-F1-Ade_S10_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	60742440.0	0.0	101.0	56.0	19.436040707896336	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S22-F1-Ade_S10_L001_R2_001	S22-F1-Ade_S10_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	60742440.0	0.0	101.0	53.0	9.214908389445387	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S22-I1-Ade_S9_L001_R1_001	S22-I1-Ade_S9_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	53295632.0	0.0	101.0	52.0	38.912306699683626	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S22-I1-Ade_S9_L001_R2_001	S22-I1-Ade_S9_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	53295632.0	0.0	101.0	52.0	19.454778648311862	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S23-A1-Ade_S7_L001_R1_001	S23-A1-Ade_S7_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	65301923.0	0.0	101.0	52.0	30.005672379589377	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S23-A1-Ade_S7_L001_R2_001	S23-A1-Ade_S7_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	65301923.0	0.0	101.0	50.0	14.196376822132276	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S23-A1-Nm_S8_L001_R1_001	S23-A1-Nm_S8_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	103014222.0	0.0	101.0	59.0	11.684797021662037	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S23-A1-Nm_S8_L001_R2_001	S23-A1-Nm_S8_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	103014222.0	0.0	101.0	56.0	6.954273374502631	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S23-B1-Ade_S1_L001_R1_001	S23-B1-Ade_S1_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	54770629.0	0.0	101.0	51.0	41.61814184689114	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S23-B1-Ade_S1_L001_R2_001	S23-B1-Ade_S1_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	54770629.0	0.0	101.0	50.0	19.430118483999152	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S23-B1-Nm_S2_L001_R1_001	S23-B1-Nm_S2_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	71922296.0	0.0	101.0	53.0	25.135563572432794	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S23-B1-Nm_S2_L001_R2_001	S23-B1-Nm_S2_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	71922296.0	0.0	101.0	51.0	11.494298368510112	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S24-B1-Ade_S5_L001_R1_001	S24-B1-Ade_S5_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	53160843.0	0.0	101.0	49.0	44.17827428241924	101.0	101	pass	warn	pass	pass	fail	warn	pass	pass	fail	fail	fail
S24-B1-Ade_S5_L001_R2_001	S24-B1-Ade_S5_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	53160843.0	0.0	101.0	48.0	22.229589463515754	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S24-B1-Nm_S6_L001_R1_001	S24-B1-Nm_S6_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	61107882.0	0.0	101.0	53.0	30.414381560554975	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S24-B1-Nm_S6_L001_R2_001	S24-B1-Nm_S6_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	61107882.0	0.0	101.0	51.0	14.11717538446005	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S24-B2-Nm_S4_L001_R1_001	S24-B2-Nm_S4_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	60595891.0	0.0	101.0	49.0	34.51787520736252	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	fail	fail	fail
S24-B2-Nm_S4_L001_R2_001	S24-B2-Nm_S4_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	60595891.0	0.0	101.0	50.0	16.583080295143443	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
S24-B2-Srp_S3_L001_R1_001	S24-B2-Srp_S3_L001_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	53407760.0	0.0	101.0	49.0	52.30181414146814	101.0	101	pass	warn	pass	pass	fail	fail	pass	pass	warn	fail	fail
S24-B2-Srp_S3_L001_R2_001	S24-B2-Srp_S3_L001_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	53407760.0	0.0	101.0	48.0	31.069354203819653	101.0	101	pass	pass	warn	pass	fail	fail	pass	pass	fail	fail	fail
