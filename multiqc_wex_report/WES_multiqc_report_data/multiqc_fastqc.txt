Sample	Filename	File type	Encoding	Total Sequences	Sequences flagged as poor quality	Sequence length	%GC	total_deduplicated_percentage	avg_sequence_length	median_sequence_length	basic_statistics	per_base_sequence_quality	per_tile_sequence_quality	per_sequence_quality_scores	per_base_sequence_content	per_sequence_gc_content	per_base_n_content	sequence_length_distribution	sequence_duplication_levels	overrepresented_sequences	adapter_content
S22-E1-Ade_S21_L006_R1_001	S22-E1-Ade_S21_L006_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	341471674.0	0.0	151.0	48.0	40.11718694435023	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	pass	fail
S22-E1-Ade_S21_L006_R2_001	S22-E1-Ade_S21_L006_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	341471674.0	0.0	151.0	48.0	7.7239028023336	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	pass	fail
S22-E1-Nm_S20_L006_R1_001	S22-E1-Nm_S20_L006_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	428184897.0	0.0	151.0	47.0	45.646935196060745	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	pass	fail
S22-E1-Nm_S20_L006_R2_001	S22-E1-Nm_S20_L006_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	428184897.0	0.0	151.0	48.0	13.539033253597015	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	pass	fail
S22-I1-Ade_S22_L006_R1_001	S22-I1-Ade_S22_L006_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	788088483.0	0.0	151.0	46.0	51.083829361913956	151.0	151	pass	pass	fail	pass	warn	warn	pass	pass	warn	pass	fail
S22-I1-Ade_S22_L006_R2_001	S22-I1-Ade_S22_L006_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	788088483.0	0.0	151.0	46.0	18.69899666286616	151.0	151	pass	pass	fail	pass	warn	warn	pass	pass	fail	warn	fail
S23-A1-Ade_S23_L006_R1_001	S23-A1-Ade_S23_L006_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	435092344.0	0.0	151.0	47.0	41.74245431200252	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	pass	fail
S23-A1-Ade_S23_L006_R2_001	S23-A1-Ade_S23_L006_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	435092344.0	0.0	151.0	47.0	8.604702961807105	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	warn	fail
S23-B1-Ade_S27_L006_R1_001	S23-B1-Ade_S27_L006_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	403298359.0	0.0	151.0	46.0	50.73475276262186	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	warn	pass	fail
S23-B1-Ade_S27_L006_R2_001	S23-B1-Ade_S27_L006_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	403298359.0	0.0	151.0	47.0	21.339052848859595	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	pass	fail
S23-B1-Nm_S26_L006_R1_001	S23-B1-Nm_S26_L006_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	396694513.0	0.0	151.0	47.0	42.825620593637176	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	pass	fail
S23-B1-Nm_S26_L006_R2_001	S23-B1-Nm_S26_L006_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	396694513.0	0.0	151.0	47.0	10.546750616535103	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	pass	fail
S24-B1-Ade_S24_L006_R1_001	S24-B1-Ade_S24_L006_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	443441812.0	0.0	151.0	45.0	50.5486701238844	151.0	151	pass	pass	fail	pass	warn	warn	pass	pass	warn	pass	fail
S24-B1-Ade_S24_L006_R2_001	S24-B1-Ade_S24_L006_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	443441812.0	0.0	151.0	45.0	21.41318085991891	151.0	151	pass	pass	fail	pass	warn	warn	pass	pass	fail	warn	fail
S24-B2-Nm_S25_L006_R1_001	S24-B2-Nm_S25_L006_R1_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	378051549.0	0.0	151.0	46.0	43.08248582269991	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	pass	fail
S24-B2-Nm_S25_L006_R2_001	S24-B2-Nm_S25_L006_R2_001.fastq.gz	Conventional base calls	Sanger / Illumina 1.9	378051549.0	0.0	151.0	46.0	10.236687062392761	151.0	151	pass	pass	fail	pass	fail	warn	pass	pass	fail	warn	fail
