// Nextflow configuration for Cell Ranger Multi Pipeline

// Process configuration
process {
    // Default LSF configuration
    executor = 'lsf'
    
    // Error handling
    errorStrategy = 'retry'
    maxRetries = 2
    
    // Resource defaults
    memory = '32.GB'
    cpus = 4
    time = '4h'
    queue = 'medium'
    
    // Process-specific configurations
    withName: createMultiConfig {
        memory = '4.GB'
        cpus = 1
        time = '30m'
        queue = 'short'
    }
    
    withName: runCellRangerMulti {
        memory = '128.GB'
        cpus = 28
        time = '24h'
        queue = 'medium'
        module = 'cellranger/9.0.1'
    }
    
    withName: generateSummaryReport {
        memory = '8.GB'
        cpus = 1
        time = '1h'
        queue = 'short'
    }
}

// Execution configuration
executor {
    name = 'lsf'
    queueSize = 10
    submitRateLimit = '5 sec'
}

// Reporting
report {
    enabled = true
    file = 'cellranger_multi_report.html'
}

timeline {
    enabled = true
    file = 'cellranger_multi_timeline.html'
}

trace {
    enabled = true
    file = 'cellranger_multi_trace.txt'
}

// Cleanup
cleanup = true
