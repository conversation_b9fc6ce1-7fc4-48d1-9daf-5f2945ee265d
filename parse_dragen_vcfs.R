#!/usr/bin/env Rscript

# Parse DRAGEN VCF Files
# Script to parse all DRAGEN filtered VCF files using the parse_vcf_csq function

# Load required packages and functions
library(tidyverse)
library(ggpubr)

# Source the VCF parsing function
source("function.R")

cat("=== DRAGEN VCF Parsing Script ===\n")
cat("Date:", as.character(Sys.time()), "\n\n")

# Find all DRAGEN VCF files
dragen_vcf_pattern <- "*.dragen.filtered.vcf.gz"
dragen_vcf_dir <- "dragen_vcf"

cat("Looking for DRAGEN VCF files in:", dragen_vcf_dir, "\n")
cat("Pattern:", dragen_vcf_pattern, "\n\n")

# Check if directory exists
if (!dir.exists(dragen_vcf_dir)) {
    cat("ERROR: Directory", dragen_vcf_dir, "does not exist.\n")
    cat("Please check the directory path.\n")
    quit(status = 1)
}

# Find all matching VCF files
dragen_vcf_files <- list.files(
    path = dragen_vcf_dir,
    pattern = "\\.dragen\\.filtered\\.vcf\\.gz$",
    full.names = TRUE,
    recursive = TRUE
)

cat("Found", length(dragen_vcf_files), "DRAGEN VCF files:\n")
if (length(dragen_vcf_files) == 0) {
    cat("No DRAGEN VCF files found matching the pattern.\n")
    cat("Please check that files exist in the directory with the correct naming pattern.\n")
    quit(status = 1)
}

# Display found files
for (i in seq_along(dragen_vcf_files)) {
    cat(sprintf("  %d. %s\n", i, basename(dragen_vcf_files[i])))
}
cat("\n")

# Function to extract sample name from VCF file path
extract_sample_name <- function(vcf_path) {
    # Extract filename and remove extensions
    filename <- basename(vcf_path)
    # Remove .dragen.filtered.vcf.gz extension
    sample_name <- str_remove(filename, "\\.dragen\\.filtered\\.vcf\\.gz$")
    return(sample_name)
}

# Parse all VCF files
cat("Starting VCF parsing...\n")

# Create output directory
output_dir <- "results/dragen_vcf_parsed"
dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

# Parse VCF files with error handling
parse_vcf_safely <- function(vcf_file) {
    sample_name <- extract_sample_name(vcf_file)
    cat("Processing:", sample_name, "...")
    
    tryCatch({
        # Parse the VCF file
        parsed_data <- parse_vcf_csq(vcf_file)
        
        # Add sample information
        parsed_data <- parsed_data %>%
            mutate(
                sample_id = sample_name,
                vcf_file = basename(vcf_file)
            )
        
        cat(" SUCCESS (", nrow(parsed_data), "variants)\n")
        return(parsed_data)
        
    }, error = function(e) {
        cat(" FAILED -", e$message, "\n")
        return(NULL)
    })
}

# Parse all files
all_dragen_variants <- map_dfr(dragen_vcf_files, parse_vcf_safely)

if (is.null(all_dragen_variants) || nrow(all_dragen_variants) == 0) {
    cat("ERROR: No variants were successfully parsed.\n")
    quit(status = 1)
}

cat("\nParsing completed successfully!\n")
cat("Total variants parsed:", nrow(all_dragen_variants), "\n")
cat("Total samples:", n_distinct(all_dragen_variants$sample_id), "\n")

# Summary statistics
cat("\nSummary by sample:\n")
sample_summary <- all_dragen_variants %>%
    group_by(sample_id) %>%
    summarise(
        total_variants = n(),
        high_impact = sum(IMPACT == "HIGH", na.rm = TRUE),
        moderate_impact = sum(IMPACT == "MODERATE", na.rm = TRUE),
        low_impact = sum(IMPACT == "LOW", na.rm = TRUE),
        modifier = sum(IMPACT == "MODIFIER", na.rm = TRUE),
        .groups = "drop"
    )

print(sample_summary)

# Save results
cat("\nSaving results...\n")

# Save combined data
write_csv(all_dragen_variants, file.path(output_dir, "all_dragen_variants.csv"))
cat("All variants saved to:", file.path(output_dir, "all_dragen_variants.csv"), "\n")

# Save summary
write_csv(sample_summary, file.path(output_dir, "dragen_variants_summary.csv"))
cat("Summary saved to:", file.path(output_dir, "dragen_variants_summary.csv"), "\n")

# Create basic plots
cat("\nGenerating plots...\n")

# Plot 1: Variant counts by sample
p1 <- sample_summary %>%
    ggplot(aes(x = sample_id, y = total_variants)) +
    geom_col(fill = "steelblue", alpha = 0.7) +
    labs(
        title = "Total Variants per Sample (DRAGEN)",
        x = "Sample ID",
        y = "Number of Variants"
    ) +
    theme_bw() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))

ggsave(file.path(output_dir, "variants_per_sample.png"), p1, width = 10, height = 6, dpi = 300)

# Plot 2: Impact distribution
impact_data <- all_dragen_variants %>%
    filter(!is.na(IMPACT)) %>%
    count(sample_id, IMPACT) %>%
    complete(sample_id, IMPACT, fill = list(n = 0))

p2 <- impact_data %>%
    ggplot(aes(x = sample_id, y = n, fill = IMPACT)) +
    geom_col(position = "stack") +
    scale_fill_manual(values = c(
        "HIGH" = "#d73027",
        "MODERATE" = "#fc8d59", 
        "LOW" = "#fee08b",
        "MODIFIER" = "#e0f3f8"
    )) +
    labs(
        title = "Variant Impact Distribution (DRAGEN)",
        x = "Sample ID",
        y = "Number of Variants",
        fill = "Impact"
    ) +
    theme_bw() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))

ggsave(file.path(output_dir, "impact_distribution.png"), p2, width = 12, height = 6, dpi = 300)

cat("Plots saved to:", output_dir, "\n")

cat("\n=== DRAGEN VCF Parsing Complete ===\n")
cat("Results available in:", output_dir, "\n")
cat("- all_dragen_variants.csv: Complete parsed data\n")
cat("- dragen_variants_summary.csv: Summary statistics\n")
cat("- variants_per_sample.png: Variant counts plot\n")
cat("- impact_distribution.png: Impact distribution plot\n")
