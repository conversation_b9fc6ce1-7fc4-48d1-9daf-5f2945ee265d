#!/usr/bin/env nextflow

// ====================================================================================
// Define script parameters for Human Neoantigen Analysis
// ====================================================================================

// --- WES Parameters ---
// IMPORTANT: Replace these paths with the actual locations of your human data and reference files.
params.metadata_csv = "/rsrch3/scratch/ccp-rsch/ndeng1/nouscom_NeoAg/meta/meta.csv"
params.fastq_dir = [
    "/rsrch8/home/<USER>/vilarsanchez/data/KS_Exome-Seq_HC378",
    "/rsrch8/home/<USER>/vilarsanchez/data/KS_Exome-Seq_HC384",
]
params.reference_genome = "/rsrch3/home/<USER>/vilarsanchez/reference_new/bwamem/GRCh38_no_alt_plus_hs38d1/GCA_000001405.15_GRCh38_no_alt_plus_hs38d1_analysis_set.fna"
params.reference_genome_idx = "/rsrch3/home/<USER>/vilarsanchez/reference_new/bwamem/GRCh38_no_alt_plus_hs38d1/GCA_000001405.15_GRCh38_no_alt_plus_hs38d1_analysis_set.fna.fai"
params.reference_genome_dict = "/rsrch3/home/<USER>/vilarsanchez/reference_new/bwamem/GRCh38_no_alt_plus_hs38d1/GCA_000001405.15_GRCh38_no_alt_plus_hs38d1_analysis_set.dict"

// DRAGMAP reference
params.dragmap_reference = "/rsrch3/home/<USER>/vilarsanchez/reference_new/dragmap/hg38_from_gatk"
params.str_table_file = "/rsrch3/home/<USER>/vilarsanchez/reference_new/dragmap/hg38_from_gatk/Homo_sapiens_assembly38.str"

// Human-specific known sites for GATK BaseRecalibrator
params.dbSNP_vcf = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/dbsnp_138.hg38.vcf.gz"
params.mills_indels_vcf = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/Mills_and_1000G_gold_standard.indels.hg38.vcf.gz"
params.known_indels_vcf = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/beta/Homo_sapiens_assembly38.known_indels.vcf.gz"

// GATK Germline Resource Parameters for VQSR
params.hapmap_vcf = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/hapmap_3.3.hg38.vcf.gz"
params.omni_vcf = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/1000G_omni2.5.hg38.vcf.gz"
params.one_k_g_vcf_vqsr = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/1000G_phase1.snps.high_confidence.hg38.vcf.gz"
params.dbSNP_vcf_vqsr = params.dbSNP_vcf
// Can reuse the same dbSNP file

// PON Germline Resource
params.germline_resource = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/Mutect2/af-only-gnomad.hg38.vcf.gz"
params.germline_resource_idx = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/Mutect2/af-only-gnomad.hg38.vcf.gz.tbi"
params.pon_vcf = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/Mutect2/1000g_pon.hg38.vcf.gz"
params.pon_vcf_idx = "/rsrch3/home/<USER>/vilarsanchez/reference_new/known_muation/hg38/Mutect2/1000g_pon.hg38.vcf.gz.tbi"
params.interval = "/rsrch3/home/<USER>/vilarsanchez/reference_new/interval/twist/hg38_exome_v2.0.2_targets_sorted_validated.re_annotated.bed"
// Can reuse the same dbSNP file
params.interval_padding = 100
params.interval_padded = "/rsrch3/home/<USER>/vilarsanchez/reference_new/interval/twist/hg38_exome_v2.0.2_targets_sorted_validated.re_annotated.padded_100bp.bed"
// Can reuse the same dbSNP file

// --- RNA-seq Parameters ---
params.rnaseq_fastq_dir = "/rsrch8/home/<USER>/vilarsanchez/data/KS_TotaRNA-Seq_R1239"
params.star_genome_dir = "/rsrch3/home/<USER>/vilarsanchez/reference_new/star/GRCh38_no_alt_plus_hs38d1"
params.gtf_annotation = "/rsrch3/home/<USER>/vilarsanchez/reference_new/annotation/gencode/GRCh38/gencode.v48.annotation.gtf"
params.cufflinks_exe = "/rsrch3/home/<USER>/ndeng1/software/cufflinks-2.2.1.Linux_x86_64/cufflinks"

// RSEM Parameters
params.star_rsem = "/rsrch3/home/<USER>/vilarsanchez/reference_new/star_rsem/GRCh38_gencode/GRCh38"
params.calc_ci = false

// --- MSMuTect & Neoantigen Parameters ---
params.msmutect_dir = "/rsrch3/home/<USER>/ndeng1/software/MSMuTect_v2.0.1/bin"
params.phobos_ref = "/rsrch3/home/<USER>/vilarsanchez/reference_new/phobos/GRCh38_no_alt_plus_hs38d1/GRCh38.phobos.filtered"
params.vt_sif = "/rsrch3/home/<USER>/ndeng1/software/vt/vt_v0.57721.sif"

// --- MSIsensor Parameters ---
params.msisensor_exec = "/rsrch3/home/<USER>/ndeng1/software/msisensor2/msisensor2/msisensor2"
params.microsatellite_list = "/home/<USER>/vilarsanchez/reference_new/msisensor/GCA_000001405.15_GRCh38_no_alt_plus_hs38d1_analysis_set.msisensor.list"

params.bcftools = "/rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools"

params.phlatdir = "/rsrch3/home/<USER>/ndeng1/software/phlat/phlat-release"
params.bgzip = "/rsrch3/home/<USER>/ndeng1/software/htslib/htslib-1.22.1/bgzip"
params.pvactools_algorithms = "all"

// --- Output Directories ---
// These are relative paths and should be fine as is.
params.mkdup_bam_output = "result/wes_bam_mkdup"
params.recal_table_output = "result/recal_tables"
params.bqsr_bam_output = "result/wes_bam_bqsr"
params.mutect_vcf_output = "result/mutect2_vcf"
params.filtered_vcf_output = "result/mutect2_filtered_vcf"
params.mutect_tumor_only_output = "result/mutect2_tumor_only"
params.msmutect_output = "result/msmutect"
params.neoantigen_preprocess_output = "result/neoantigen_preprocess"
params.somatic_msi_output = "result/somatic_msi"
params.msisensor_output = "result/msisensor"
params.germline_vcf_output = "result/germline_vcf"
params.final_vcf_output = "result/final_somatic_vcfs"
params.bam_readcount_output = "result/bam_readcount"
// RNA-seq Outputs
params.rnaseq_bam_output = "result/RNAseq_bam"
params.cufflinks_output = "result/cufflinks_out"
params.rsem_output = "result/rsem_counts"
params.qualimap_output = "result/qualimap_rnaseq"
// QC Outputs
params.fastqc_output = "result/fastqc_raw"
params.multiqc_wex_output = "result/multiqc_wex_report"
params.multiqc_rnaseq_output = "result/multiqc_rnaseq_report"
// Pvactools output
params.pvactools_output = "result/pvactools"
params.phlat_output = "result/PHLAT"
// HLA typing output
params.hla_typing_output = "result/hla_typing"
// DRAGMAP outputs
params.dragmap_bam_output = "result/dragmap_bam"
params.dragstr_model_output = "result/dragstr_models"
params.dragen_vcf_output = "result/dragen_vcf"
params.bowtie2 = "/rsrch3/home/<USER>/ndeng1/software/bowtie2/bowtie2-2.5.4-linux-x86_64/bowtie2"

params.vep_species = "homo_sapiens"
params.vep_genome_build = "GRCh38"
params.vep_plugin = "/rsrch3/home/<USER>/ndeng1/software/vep/pvactools_plugin"
// Please provide correct path
params.vep_cache = "/rsrch3/home/<USER>/ndeng1/software/vep/vep_data_114_1"
// Please provide correct path

// ====================================================================================
//  PROCESSES: SHARED QC
// ====================================================================================
process runFastQC {
    tag "FastQC on ${fastq_file.name}"
    // publishDir "${params.fastqc_output}/${data_type}/${sample_name}", mode: 'copy', saveAs: { filename -> filename.indexOf('_fastqc') > 0 ? filename : null }

    executor 'lsf'
    queue 'short'
    memory '4.GB'
    cpus 1
    time '2h'
    module 'fastqc/0.11.9-non-conda'

    input:
    tuple val(sample_name), val(data_type), path(fastq_file)

    output:
    tuple val(sample_name), val(data_type), path("*_fastqc.{zip,html}")

    script:
    "fastqc --outdir . \"${fastq_file}\""
}

process runMultiQC {
    tag "MultiQC for ${data_type}"
    publishDir "${output_dir}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '8.GB'
    cpus 1
    time '1h'
    module 'multiqc/1.25.2'

    input:
    tuple val(data_type), val(output_dir), path(qc_files)

    output:
    path "*"

    script:
    "multiqc . -n ${data_type}_multiqc_report"
}

// ====================================================================================
//  PROCESSES: DRAGEN MODE SINGLE SAMPLE VARIANT CALLING
// ====================================================================================

process dragmapAlignment {
    tag "DRAGMAP alignment for ${sample_name}"
    publishDir "${params.dragmap_bam_output}", mode: 'copy', pattern: "${sample_name}.dragmap.bam*"

    executor 'lsf'
    queue 'medium'
    memory '129.GB'
    cpus 28
    time '12h'
    module 'dragmap/1.3.0-non-conda'

    input:
    tuple val(sample_name), path(reads)
    path dragmap_reference_dir

    output:
    tuple val(sample_name), path("${sample_name}.dragmap.bam"), path("${sample_name}.dragmap.bam.bai")

    script:
    def read1 = reads[0]
    def read2 = reads[1]
    """
                # DRAGMAP alignment
                dragen-os \\
                    -r ${dragmap_reference_dir} \\
                    -1 ${read1} \\
                    -2 ${read2} \\
                    --num-threads ${task.cpus} \\
                    | samtools view -@ ${task.cpus} -bS - \\
                    | samtools sort -@ ${task.cpus} -o ${sample_name}.dragmap.bam -

                # Index the BAM file
                samtools index ${sample_name}.dragmap.bam
                """
}

process calibrateDragstrModel {
    tag "Calibrate DRAGSTR model for ${sample_name}"
    publishDir "${params.dragstr_model_output}", mode: 'copy', pattern: "*.dragstr_model.txt"

    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 4
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(bam), path(bai)

    output:
    tuple val(sample_name), path("${sample_name}.dragstr_model.txt")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                gatk CalibrateDragstrModel \\
                    -R ${params.dragmap_reference}/Homo_sapiens_assembly38_masked.fasta \\
                    -I ${bam} \\
                    -str ${params.str_table_file} \\
                    -O ${sample_name}.dragstr_model.txt
                """
}

process haplotypeCallerDragenModeByInterval {
    tag "HaplotypeCaller DRAGEN mode for ${sample_name} interval ${interval_name}"
    publishDir "${params.dragen_vcf_output}/${sample_name}/intervals", mode: 'copy'

    executor 'lsf'
    queue 'medium'
    memory '32.GB'
    cpus 4
    time '8h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(bam), path(bai), path(dragstr_model), val(interval_name), path(interval_file)

    output:
    tuple val(sample_name), val(interval_name), path("${sample_name}.${interval_name}.dragen.raw.vcf.gz"), path("${sample_name}.${interval_name}.dragen.raw.vcf.gz.tbi")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                gatk HaplotypeCaller \\
                    -R ${params.dragmap_reference}/Homo_sapiens_assembly38_masked.fasta \\
                    -I ${bam} \\
                    -O ${sample_name}.${interval_name}.dragen.raw.vcf.gz \\
                    --dragen-mode true \\
                    --dragstr-params-path ${dragstr_model} \\
                    -L ${interval_file}
                """
}

process mergeDragenVCFs {
    tag "Merge DRAGEN VCFs for ${sample_name}"
    publishDir "${params.dragen_vcf_output}/${sample_name}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '16.GB'
    cpus 2
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(vcfs), path(tbis)

    output:
    tuple val(sample_name), path("${sample_name}.dragen.raw.vcf.gz"), path("${sample_name}.dragen.raw.vcf.gz.tbi")

    script:
    def sorted_vcfs = vcfs.sort { a, b ->
        def a_num = a.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        def b_num = b.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        return a_num <=> b_num
    }

    def vcf_args = sorted_vcfs.collect { "-I ${it}" }.join(' ')
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                gatk GatherVcfs \\
                    ${vcf_args} \\
                    -O ${sample_name}.dragen.raw.vcf.gz

                ${params.bcftools} index -t ${sample_name}.dragen.merged.vcf.gz

                """
}

process dragenHardFilter {
    tag "DRAGEN hard filter for ${sample_name}"
    publishDir "${params.dragen_vcf_output}/${sample_name}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '16.GB'
    cpus 2
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(raw_vcf), path(raw_tbi)

    output:
    tuple val(sample_name), path("${sample_name}.dragen.filtered.vcf.gz"), path("${sample_name}.dragen.filtered.vcf.gz.tbi")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                gatk VariantFiltration \\
                    -V ${raw_vcf} \\
                    --filter-expression "QUAL < 10.4139" \\
                    --filter-name "DRAGENHardQUAL" \\
                    -O ${sample_name}.dragen.filtered.vcf.gz
                """
}


// ====================================================================================
//  PROCESSES: WES PRE-PROCESSING
// ====================================================================================
process bwaMemAlignment {
    tag "Aligning & Sorting ${sample_name}"

    executor 'lsf'
    queue 'medium'
    memory '512.GB'
    cpus 28
    time '24h'
    module 'bwa/0.7.19'

    input:
    tuple val(sample_name), path(reads)

    output:
    tuple val(sample_name), path("${sample_name}.bam"), path("${sample_name}.bam.bai")

    script:
    def rg_string = "@RG\\tID:${sample_name}\\tSM:${sample_name}\\tPL:Illumina\\tLB:lib1\\tPU:unit1"
    """
                bwa mem -t ${task.cpus} -R '${rg_string}' "${params.reference_genome}" "${reads[0]}" "${reads[1]}" | samtools view -Sb - | samtools sort -@ ${task.cpus - 1} -o "${sample_name}.bam" -
                samtools index "${sample_name}.bam"
                """
}

process markDuplicatesSpark {
    tag "Marking Duplicates for ${sample_name}"
    // publishDir "${params.mkdup_bam_output}", mode: 'copy', pattern: "${sample_name}.mkdup.*"

    executor 'lsf'
    queue 'medium'
    memory '512.GB'
    cpus 28
    time '24h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(bam_file), path(bai_file)

    output:
    tuple val(sample_name), path("${sample_name}.mkdup.bam"), path("${sample_name}.mkdup.bam.bai")

    script:
    def mem_gb = task.memory.toGiga() - 2
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk --java-options "-Xmx${mem_gb}G" MarkDuplicatesSpark --tmp-dir . -I "${bam_file}" -O "${sample_name}.mkdup.bam" -M "${sample_name}.mkdup.metrics.txt" -- --spark-master local[${task.cpus}]
                samtools index "${sample_name}.mkdup.bam"
                """
}

process baseRecalibrator {
    tag "BaseRecalibrator for ${sample_name}"
    // publishDir "${params.recal_table_output}", mode: 'copy', pattern: "*.recal.table"
    executor 'lsf'
    queue 'medium'
    memory '256.GB'
    cpus 28
    time '24h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(bam_file), path(bai_file)
    path known_sites_files

    output:
    tuple val(sample_name), path(bam_file), path("*.recal.table")

    script:
    // Filter the input list to get only VCF files for the GATK command.
    // The .tbi files are also staged in the work directory, and GATK will find them automatically.
    def vcfs_only = known_sites_files.findAll { it.name.endsWith('.vcf.gz') }
    def known_sites_args = vcfs_only.collect { "--known-sites ${it}" }.join(' ')
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk BaseRecalibrator --tmp-dir . -R "${params.reference_genome}" -I "${bam_file}" ${known_sites_args} -O "${sample_name}.recal.table"
                """
}

process applyBQSR {
    tag "ApplyBQSR for ${sample_name}"
    publishDir "${params.bqsr_bam_output}", mode: 'copy', pattern: "${sample_name}.bqsr.bam*"
    executor 'lsf'
    queue 'medium'
    memory '8.GB'
    cpus 28
    time '24h'
    module 'gatk4/*******'

    input:
    tuple val(sample_name), path(bam_file), path(recal_table)

    output:
    tuple val(sample_name), path("${sample_name}.bqsr.bam"), path("${sample_name}.bqsr.bam.bai")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk ApplyBQSR --tmp-dir . -R "${params.reference_genome}" -I "${bam_file}" --bqsr-recal-file "${recal_table}" -O "${sample_name}.bqsr.bam"
                samtools index "${sample_name}.bqsr.bam"
                """
}

// ====================================================================================
//  PROCESSES: WES GERMLINE CALLING (GATK Best Practices)
// ====================================================================================
process haplotypeCallerSingle {
    tag "HaplotypeCaller on ${sample_id} for interval ${interval_name}"
    // publishDir "${params.germline_vcf_output}/${sample_id}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '256.GB'
    cpus 28
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_id), path(bam), path(bai), val(interval_name), path(interval_file)
    path reference
    path reference_idx
    path reference_dict

    output:
    tuple val(sample_id), val(interval_name), path("*.g.vcf.gz"), path("*.g.vcf.gz.tbi")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk HaplotypeCaller -R ${reference} -I ${bam} -O "${sample_id}.${interval_name}.g.vcf.gz" -ERC GVCF -L ${interval_file}
            """
}

process genomicsDBImportGermline {
    tag "GenomicsDBImport Germline for cohort interval ${interval_name}"
    executor 'lsf'
    queue 'short'
    memory '128.GB'
    cpus 1
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(interval_name), path(gvcfs), path(gvcf_tbis), path(interval_file)
    path reference
    path reference_dict

    output:
    tuple val(interval_name), path("germline_db.${interval_name}")

    script:
    def gvcf_args = gvcfs.collect { "-V ${it}" }.join(' ')
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                gatk GenomicsDBImport \\
                    ${gvcf_args} \\
                    --genomicsdb-workspace-path germline_db.${interval_name} \\
                    --intervals ${interval_file} \\
                    --merge-input-intervals \\
                    --tmp-dir /rsrch3/scratch/ccp-rsch/ndeng1/nouscom_NeoAg/tmp

                """
}

process genotypeGVCFs {
    tag "GenotypeGVCFs for cohort interval ${interval_name}"
    executor 'lsf'
    queue 'medium'
    memory '256.GB'
    cpus 3
    time '24h'
    module 'gatk4/*******'

    input:
    tuple val(interval_name), path(genomicsdb), path(interval_file)
    path reference
    path reference_idx
    path reference_dict

    output:
    tuple val(interval_name), path("cohort.${interval_name}.raw.vcf.gz"), path("cohort.${interval_name}.raw.vcf.gz.tbi")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk GenotypeGVCFs \\
                    -R ${reference} \\
                    -V gendb://germline_db.${interval_name} \\
                    -O cohort.${interval_name}.raw.vcf.gz \\
                    --intervals ${interval_file}
                """
}

process combineIntervalVCFs {
    tag "Combine interval VCFs for cohort"
    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 1
    time '2h'
    module 'gatk4/*******'

    input:
    path interval_vcfs
    path interval_tbis
    path reference

    output:
    tuple path("cohort.raw.vcf.gz"), path("cohort.raw.vcf.gz.tbi")

    script:
    // Sort VCFs by interval name to ensure proper order (part_00, part_01, ..., part_30)
    def sorted_vcfs = interval_vcfs.sort { a, b ->
        def a_num = a.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        def b_num = b.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        return a_num <=> b_num
    }
    def vcf_args = sorted_vcfs.collect { "-I ${it}" }.join(' ')
    """

                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                gatk GatherVcfs \\
                    ${vcf_args} \\
                    -O cohort.raw.vcf.gz \\
                    -R ${reference}

                ${params.bcftools} index -t cohort.raw.vcf.gz
                """
}

process runVQSR {
    tag "Running VQSR"
    publishDir "${params.germline_vcf_output}", mode: 'copy'
    executor 'lsf'
    queue 'short'
    memory '16.GB'
    cpus 2
    time '3h'
    module 'gatk4/*******'
    module 'R/4.5.1'

    input:
    tuple path(raw_vcf), path(raw_tbi)
    path hapmap
    path hapmap_tbi
    path omni
    path omni_tbi
    path one_k_g
    path one_k_g_tbi
    path dbsnp
    path dbsnp_tbi
    path mills_indels
    path mills_indels_tbi
    path reference
    path reference_idx
    path reference_dict

    output:
    tuple path("cohort.filtered.vcf.gz"), path("cohort.filtered.vcf.gz.tbi")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                
                # SNP Recalibration
                gatk VariantRecalibrator \\
                    -R ${reference} \\
                    -V ${raw_vcf} \\
                    -resource:hapmap,known=false,training=true,truth=true,prior=15.0 ${hapmap} \\
                    -resource:omni,known=false,training=true,truth=true,prior=12.0 ${omni} \\
                    -resource:1000G,known=false,training=true,truth=false,prior=10.0 ${one_k_g} \\
                    -resource:dbsnp,known=true,training=false,truth=false,prior=2.0 ${dbsnp} \\
                    -an QD -an MQ -an MQRankSum -an ReadPosRankSum -an FS -an SOR \\
                    -mode SNP \\
                    -O cohort.snps.recal \\
                    --tranches-file cohort.snps.tranches \\
                    --rscript-file cohort.snps.plots.R \\
                    --intervals ${params.interval} \\
                    --interval-padding ${params.interval_padding}

                gatk ApplyVQSR \\
                    -R ${reference} \\
                    -V ${raw_vcf} \\
                    --recal-file cohort.snps.recal \\
                    --tranches-file cohort.snps.tranches \\
                    -mode SNP \\
                    -O cohort.snps.recalibrated.vcf.gz \\
                    --intervals ${params.interval} \\
                    --interval-padding ${params.interval_padding}

                # INDEL Recalibration
                gatk VariantRecalibrator \\
                    -R ${reference} \\
                    -V cohort.snps.recalibrated.vcf.gz \\
                    -resource:mills,known=false,training=true,truth=true,prior=12.0 ${mills_indels} \\
                    -resource:dbsnp,known=true,training=false,truth=false,prior=2.0 ${dbsnp} \\
                    -an QD -an DP -an FS -an SOR -an ReadPosRankSum -an MQRankSum \\
                    -mode INDEL \\
                    -O cohort.indels.recal \\
                    --tranches-file cohort.indels.tranches \\
                    --rscript-file cohort.indels.plots.R \\
                    --intervals ${params.interval} \\
                    --interval-padding ${params.interval_padding}

                gatk ApplyVQSR \\
                    -R ${reference} \\
                    -V cohort.snps.recalibrated.vcf.gz \\
                    --recal-file cohort.indels.recal \\
                    --tranches-file cohort.indels.tranches \\
                    -mode INDEL \\
                    -O cohort.filtered.vcf.gz \\
                    --intervals ${params.interval} \\
                    --interval-padding ${params.interval_padding}
                
                

                
                """
}

process filterGermlineByNormal {
    tag "Filter germline VCF by normal samples"
    publishDir "${params.germline_vcf_output}", mode: 'copy'
    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 4
    time '3h'

    input:
    tuple path(cohort_vcf), path(cohort_tbi), val(normal_id)

    output:
    tuple val(normal_id), path("*.germline.trimmed.vcf.gz"), path("*.germline.trimmed.vcf.gz.tbi")

    script:
    """

                ${params.bcftools} view -s ${normal_id} ${cohort_vcf} | \\
                ${params.bcftools} view -f PASS | \\
                ${params.bcftools} view -e 'GT="0/0"' -Oz -o ${normal_id}.germline.trimmed.vcf.gz
                ${params.bcftools} index -t ${normal_id}.germline.trimmed.vcf.gz

                """
}


// ====================================================================================
//  PROCESSES: PANEL OF NORMALS (PON) GENERATION
// ====================================================================================




process runMutect2Normal {
    tag "Mutect2 on normal ${sample_id} for interval ${interval_name}"
    executor 'lsf'
    queue 'short'
    memory '256.GB'
    cpus 28
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_id), path(bam), path(bai), val(interval_name), path(interval_file)
    path reference
    path reference_idx
    path reference_dict

    output:
    tuple val(sample_id), val(interval_name), path("*.vcf.gz"), path("*.vcf.gz.tbi")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk Mutect2 \\
                    -R ${reference} \\
                    -I ${bam} \\
                    --max-mnp-distance 0 \\
                    -O "${sample_id}.${interval_name}.vcf.gz" \\
                    -L ${interval_file}
                """
}

process genomicsDBImportPON {
    tag "GenomicsDBImport PON for interval ${interval_name}"
    executor 'lsf'
    queue 'short'
    memory '128.GB'
    cpus 1
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(interval_name), path(vcfs), path(vcf_tbis), path(interval_file)
    path reference
    path reference_idx
    path reference_dict

    output:
    tuple val(interval_name), path("pon_db.${interval_name}")

    script:
    def vcf_args = vcfs.collect { "-V ${it}" }.join(' ')
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                gatk GenomicsDBImport \\
                    -R ${reference} \\
                    -L ${interval_file} \\
                    --genomicsdb-workspace-path pon_db.${interval_name} \\
                    ${vcf_args} \\
                    --merge-input-intervals \\
                    --tmp-dir /rsrch3/scratch/ccp-rsch/ndeng1/nouscom_NeoAg/tmp
                """
}

process createSomaticPanelOfNormals {
    tag "CreateSomaticPanelOfNormals for interval ${interval_name}"
    executor 'lsf'
    queue 'short'
    memory '128.GB'
    cpus 1
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(interval_name), path(genomicsdb), path(interval_file)
    path reference
    path reference_idx
    path reference_dict
    path germline_resource
    path germline_resource_idx

    output:
    tuple val(interval_name), path("pon.${interval_name}.vcf.gz"), path("pon.${interval_name}.vcf.gz.tbi")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                # Create PON from GenomicsDB for this interval
                gatk CreateSomaticPanelOfNormals \\
                    -R ${reference} \\
                    --germline-resource ${germline_resource} \\
                    -V gendb://pon_db.${interval_name} \\
                    -O pon.${interval_name}.vcf.gz \\
                    -L ${interval_file}
                """
}

process gatherPONVCFs {
    tag "Gather interval PON VCFs"
    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 1
    time '3h'
    module 'gatk4/*******'

    input:
    path pon_vcfs
    path pon_tbis
    path reference

    output:
    tuple path("pon.combined.vcf.gz"), path("pon.combined.vcf.gz.tbi")

    script:
    // Sort PON VCFs by interval name to ensure proper order (part_00, part_01, ..., part_30)
    def sorted_vcfs = pon_vcfs.sort { a, b ->
        def a_num = a.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        def b_num = b.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        return a_num <=> b_num
    }
    def vcf_args = sorted_vcfs.collect { "-I ${it}" }.join(' ')
    def bcftools = "/rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools"
    // Please provide correct path


    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                gatk GatherVcfs \\
                    ${vcf_args} \\
                    -O pon.combined.vcf.gz \\
                    -R ${reference}

                ${bcftools} index -t pon.combined.vcf.gz


                """
}



// ====================================================================================
//  PROCESSES: WES SOMATIC CALLING
// ====================================================================================
process runMutect2 {
    tag "Mutect2 on ${sample_id} for interval ${interval_name}"
    // publishDir "${params.mutect_vcf_output}/${sample_id}/${interval_name}", mode: 'copy'
    executor 'lsf'
    queue 'short'
    memory '256.GB'
    cpus 28
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_id), val(tumor_name), path(tumor_bam), path(tumor_bai), val(normal_names), path(normal_bams), path(normal_bais), val(interval_name), path(interval_file)
    path reference
    path reference_idx
    path reference_dict
    tuple path(pon_vcf), path(pon_tbi)
    path germline_resource
    path germline_resource_idx

    output:
    tuple val(sample_id), val(interval_name), path("*.vcf.gz"), path("*.vcf.gz.tbi"), path("*_f1r2.tar.gz"), path("*.vcf.gz.stats")

    script:
    def normal_bam_args = normal_bams.collect { "-I ${it}" }.join(' ')
    def normal_name_args = normal_names.collect { "-normal ${it}" }.join(' ')
    """

                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                
                gatk Mutect2 --tmp-dir . --native-pair-hmm-threads 28 -R ${reference} -I ${tumor_bam} ${normal_bam_args} ${normal_name_args} --f1r2-tar-gz ${sample_id}_${interval_name}_f1r2.tar.gz -O "${sample_id}.${interval_name}.mutect2.raw.vcf.gz" -L ${interval_file} --panel-of-normals ${pon_vcf} --germline-resource ${germline_resource}
                """
}

process getPileupSummaries {
    tag "GetPileupSummaries for ${sample_id} ${sample_type}"
    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 1
    time '2h'
    module 'gatk4/*******'

    input:
    tuple val(sample_id), val(sample_type), path(bam), path(bai)
    path germline_resource
    path germline_resource_idx
    path reference

    output:
    tuple val(sample_id), val(sample_type), path("*.getpileupsummaries.table")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk GetPileupSummaries \\
                    -I ${bam} \\
                    -V ${germline_resource} \\
                    -L ${params.interval} \\
                    -O "${sample_id}.${sample_type}.getpileupsummaries.table"
                """
}

process calculateContamination {
    tag "CalculateContamination for ${sample_id}"
    executor 'lsf'
    queue 'short'
    memory '8.GB'
    cpus 1
    time '1h'
    module 'gatk4/*******'

    input:
    tuple val(sample_id), path(tumor_pileup)
    path reference

    output:
    tuple val(sample_id), path("*.contamination.table")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk CalculateContamination \\
                    -I ${tumor_pileup} \\
                    -O "${sample_id}.contamination.table"
                """
}

process learnReadOrientationModel {
    tag "LearnReadOrientationModel for ${sample_id}"
    executor 'lsf'
    queue 'short'
    memory '64.GB'
    cpus 1
    time '1h'
    module 'gatk4/*******'

    input:
    tuple val(sample_id), path(f1r2_tar_gz_files), path(stats_files)

    output:
    tuple val(sample_id), path("*.read-orientation-model.tar.gz"), path("*.merged.filtered.stats")

    script:
    def f1r2_args = f1r2_tar_gz_files.collect { "-I ${it}" }.join(' ')
    def stats_args = stats_files.collect { "-stats ${it}" }.join(' ')
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk LearnReadOrientationModel ${f1r2_args} -O "${sample_id}.read-orientation-model.tar.gz"
                # Merge stat files
                gatk MergeMutectStats \
                    ${stats_args} \
                    -O "${sample_id}.merged.filtered.stats"
                """
}

process runMSIsensor {
    tag "MSIsensor on ${sample_id}"
    publishDir "${params.msisensor_output}/${sample_id}", mode: 'copy'
    executor 'lsf'
    queue 'medium'
    memory '128.GB'
    cpus 28
    time '24h'
    module 'samtools'

    input:
    tuple val(sample_id), val(tumor_name), path(tumor_bam), path(tumor_bai), path(normal_bams)

    output:
    path "${sample_id}"
    path "${sample_id}_somatic"
    path "${sample_id}_germline"
    path "${sample_id}_dis"

    script:
    def normal_bam_input = normal_bams.size() > 1 ? "merged_normal.bam" : normal_bams[0]
    def merge_command = normal_bams.size() > 1
        ? """
                    samtools merge -@ ${task.cpus - 1} ${normal_bam_input} ${normal_bams.join(' ')}
                    samtools index ${normal_bam_input}
                    """
        : ""
    """
                ${merge_command}
                ${params.msisensor_exec} msi -t ${task.cpus} -d "${params.microsatellite_list}" -n "${normal_bam_input}" -t "${tumor_bam}" -o "${sample_id}"
                """
}

process filterMutectCalls {
    tag "Filtering Mutect2 for ${sample_id} interval ${interval_name}"
    executor 'lsf'
    queue 'short'
    memory '256.GB'
    cpus 28
    time '3h'
    module 'gatk4/*******'

    input:
    tuple val(sample_id), path(ob_priors), path(stats), path(contamination_table), val(interval_name), path(raw_vcf), path(raw_tbi), path(f1r2_dump), path(stats_dump)

    output:
    tuple val(sample_id), path("*.filtered.vcf.gz"), path("*.filtered.vcf.gz.tbi")

    script:
    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u
                gatk FilterMutectCalls \\
                    -R ${params.reference_genome} \\
                    -V ${raw_vcf} \\
                    --ob-priors ${ob_priors} \\
                    --contamination-table ${contamination_table} \\
                    -O "${sample_id}.${interval_name}.mutect2.filtered.vcf.gz" \\
                    --stats "${stats}"
                """
}

process mergeFilteredMutectCalls {
    tag "Merge filtered Mutect2 calls for ${sample_id}"
    publishDir "${params.filtered_vcf_output}/${sample_id}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 1
    time '2h'
    module 'gatk4/*******'

    input:
    tuple val(sample_id), path(filtered_vcfs), path(fiiltered_tbis)
    path reference

    output:
    tuple val(sample_id), path("*.merged.filtered.vcf.gz"), path("*.merged.filtered.vcf.gz.tbi")

    script:
    // Sort VCFs by interval name to ensure proper order (part_00, part_01, ..., part_30)
    def sorted_vcfs = filtered_vcfs.sort { a, b ->
        def a_num = a.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        def b_num = b.name.replaceAll(/.*part_(\d+).*/, '$1').toInteger()
        return a_num <=> b_num
    }
    def vcf_args = sorted_vcfs.collect { "-I ${it}" }.join(' ')
    def bcftools = "/rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools"
    // Please provide correct path

    """
                set +u
                source /risapps/rhel8/miniforge3/24.5.0-0/etc/profile.d/conda.sh
                conda activate gatk4-*******
                set -u

                # Merge VCFs
                gatk GatherVcfs \
                    ${vcf_args} \
                    -O "${sample_id}.merged.filtered.vcf.gz" \
                    -R ${reference}

                ${bcftools} index -t ${sample_id}.merged.filtered.vcf.gz
                """
}

// ====================================================================================
//  PROCESSES: TUMOR-ONLY MUTECT2 PIPELINE
// ====================================================================================

process runMutect2TumorOnly {
    tag "Mutect2 tumor-only on ${sample_id} for interval ${interval_name}"

    executor 'lsf'
    queue 'medium'
    memory '64.GB'
    cpus 8
    time '12h'

    input:
    tuple val(sample_id), val(interval_name), path(tumor_bam), path(tumor_bai)

    output:
    tuple val(sample_id), val(interval_name), path("${sample_id}.${interval_name}.mutect2.vcf.gz"), path("${sample_id}.${interval_name}.mutect2.vcf.gz.tbi"), path("${sample_id}.${interval_name}.mutect2.vcf.gz.stats"), path("${sample_id}.${interval_name}.f1r2.tar.gz")

    script:
    """
    ${params.gatk} --java-options "-Xmx${task.memory.toGiga() - 4}g" Mutect2 \\
        -R ${params.reference_genome} \\
        -I ${tumor_bam} \\
        --germline-resource ${params.germline_resource} \\
        --panel-of-normals ${params.pon_vcf} \\
        -L ${params.interval_padded} \\
        --interval-padding ${params.interval_padding} \\
        --f1r2-tar-gz ${sample_id}.${interval_name}.f1r2.tar.gz \\
        -O ${sample_id}.${interval_name}.mutect2.vcf.gz
    """
}

process getContaminationPileups {
    tag "GetPileupSummaries for contamination ${sample_id}"

    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 4
    time '6h'

    input:
    tuple val(sample_id), path(tumor_bam), path(tumor_bai)

    output:
    tuple val(sample_id), path("${sample_id}.pileups.table")

    script:
    """
    ${params.gatk} --java-options "-Xmx${task.memory.toGiga() - 4}g" GetPileupSummaries \\
        -I ${tumor_bam} \\
        -V ${params.germline_resource} \\
        -L ${params.interval_padded} \\
        -O ${sample_id}.pileups.table
    """
}

process calculateContaminationTumorOnly {
    tag "CalculateContamination tumor-only for ${sample_id}"

    executor 'lsf'
    queue 'short'
    memory '16.GB'
    cpus 2
    time '3h'

    input:
    tuple val(sample_id), path(pileups_table)

    output:
    tuple val(sample_id), path("${sample_id}.contamination.table"), path("${sample_id}.segments.table")

    script:
    """
    ${params.gatk} --java-options "-Xmx${task.memory.toGiga() - 2}g" CalculateContamination \\
        -I ${pileups_table} \\
        -O ${sample_id}.contamination.table \\
        --tumor-segmentation ${sample_id}.segments.table
    """
}

process learnReadOrientationModelTumorOnly {
    tag "LearnReadOrientationModel tumor-only for ${sample_id}"

    executor 'lsf'
    queue 'short'
    memory '16.GB'
    cpus 2
    time '3h'

    input:
    tuple val(sample_id), path(f1r2_files)

    output:
    tuple val(sample_id), path("${sample_id}.read-orientation-model.tar.gz")

    script:
    f1r2_args = f1r2_files.collect { "-I ${it}" }.join(' ')
    """
    ${params.gatk} --java-options "-Xmx${task.memory.toGiga() - 2}g" LearnReadOrientationModel \\
        ${f1r2_args} \\
        -O ${sample_id}.read-orientation-model.tar.gz
    """
}

process filterMutectCallsTumorOnly {
    tag "FilterMutectCalls tumor-only for ${sample_id} interval ${interval_name}"

    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 4
    time '6h'

    input:
    tuple val(sample_id), val(interval_name), path(vcf), path(vcf_idx), path(stats), path(contamination_table), path(segments_table), path(read_orientation_model)

    output:
    tuple val(sample_id), val(interval_name), path("${sample_id}.${interval_name}.filtered.vcf.gz"), path("${sample_id}.${interval_name}.filtered.vcf.gz.tbi")

    script:
    """
    ${params.gatk} --java-options "-Xmx${task.memory.toGiga() - 4}g" FilterMutectCalls \\
        -R ${params.reference_genome} \\
        -V ${vcf} \\
        --contamination-table ${contamination_table} \\
        --tumor-segmentation ${segments_table} \\
        --ob-priors ${read_orientation_model} \\
        --stats ${stats} \\
        -O ${sample_id}.${interval_name}.filtered.vcf.gz
    """
}

process mergeFilteredMutectCallsTumorOnly {
    tag "Merge filtered Mutect2 tumor-only calls for ${sample_id}"
    publishDir "${params.mutect_tumor_only_output}/${sample_id}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '32.GB'
    cpus 4
    time '6h'

    input:
    tuple val(sample_id), path(vcfs), path(vcf_indices)

    output:
    tuple val(sample_id), path("${sample_id}.mutect2.tumor_only.filtered.vcf.gz"), path("${sample_id}.mutect2.tumor_only.filtered.vcf.gz.tbi")

    script:
    def sorted_vcfs = vcfs.sort { a, b ->
        def a_num = a.name.replaceAll(/.*\.(\d+)\..*/, '$1').toInteger()
        def b_num = b.name.replaceAll(/.*\.(\d+)\..*/, '$1').toInteger()
        return a_num <=> b_num
    }
    def vcf_args = sorted_vcfs.collect { "-I ${it}" }.join(' ')
    """
    ${params.gatk} --java-options "-Xmx${task.memory.toGiga() - 4}g" MergeVcfs \\
        ${vcf_args} \\
        -O ${sample_id}.mutect2.tumor_only.filtered.vcf.gz

    ${params.bcftools} index -t ${sample_id}.mutect2.tumor_only.filtered.vcf.gz
    """
}

// ====================================================================================
//  PROCESSES: MSMuTect and Neoantigen Pre-processing
// ====================================================================================
process runMSMuTect {
    tag "MSMuTect on ${sample_name}"
    publishDir "${params.msmutect_output}/${sample_name}", mode: 'copy', pattern: "*.hist.mot"

    executor 'lsf'
    queue 'medium'
    memory '256.GB'
    cpus 24
    time '24h'
    module 'python/3.11.3'

    input:
    tuple val(sample_name), path(bam_file), path(bai_file)
    path phobos_ref

    output:
    tuple val(sample_name), path("*.hist.mot")

    script:
    def msmutect_py = "${params.msmutect_dir}/msmutect.py"
    def hist2py_sh = "${params.msmutect_dir}/hist2py.sh"
    """
                python ${msmutect_py} -I ${bam_file} -l ${phobos_ref} -O ${sample_name}.hist
                sh ${hist2py_sh} ${sample_name}.hist

                """
}

process processSomaticMSI {
    tag "Somatic MSI for ${sample_id}, motif ${motif}"

    executor 'lsf'
    queue 'short'
    memory '16.GB'
    cpus 4
    time '2h'
    module 'python'
    module 'R'

    input:
    tuple val(sample_id), val(motif), path(tumor_hist_mot), path(normal_hist_mots)
    path phobos_ref

    output:
    tuple val(sample_id), val(motif), path("*.mut.maf_like.dec")

    script:

    def get_all_py = "${params.msmutect_dir}/get_all.py"
    def format_sh = "/rsrch8/home/<USER>/vilarsanchez/archive/ndeng/all_neoantigen/workflows/ndeng/format-alleles.sh"
    def identify_sh = "/rsrch8/home/<USER>/vilarsanchez/archive/ndeng/all_neoantigen/workflows/ndeng//identify-shared-loci.sh"
    // Please provide correct path
    def find_muts_py = "${params.msmutect_dir}/Find_mutations2.py"
    def make_maf_sh = "/rsrch8/home/<USER>/vilarsanchez/archive/ndeng/all_neoantigen/workflows/ndeng/make-maf-like.sh"
    // Please provide correct path
    def add_base_sh = "/rsrch8/home/<USER>/vilarsanchez/archive/ndeng/all_neoantigen/workflows/ndeng/add-altered-base.sh"
    // Please provide correct path
    def merge_mot_r = "/rsrch3/home/<USER>/ndeng1/R_script/merge_mot.R"


    def normal_input_args = normal_hist_mots.collect { "-I ${it}" }.join(' ')
    def out_prefix = "${sample_id}_P_${motif}"

    """
                # 1. Process Normal Samples
                Rscript ${merge_mot_r} ${normal_input_args} -O ${sample_id}.normal.mot
                python ${get_all_py} ${sample_id}.normal.mot ${params.msmutect_dir}/P_${motif}.csv ${sample_id}.normal.mot.all
                sh ${format_sh} ${sample_id}.normal.mot.all ${sample_id}.normal.mot.all.tmp

                # 2. Process Tumor Sample
                Rscript ${merge_mot_r} -I ${tumor_hist_mot} -O ${sample_id}.tumor.mot
                python ${get_all_py} ${sample_id}.tumor.mot ${params.msmutect_dir}/P_${motif}.csv ${sample_id}.tumor.mot.all
                sh ${format_sh} ${sample_id}.tumor.mot.all ${sample_id}.tumor.mot.all.tmp

                # 3. Identify Somatic MSI
                sh ${identify_sh} ${sample_id}.tumor.mot.all.tmp ${sample_id}.normal.mot.all.tmp ${sample_id}
                
                python ${find_muts_py} \
                    ${sample_id}.normal.mot.all.tmp.par.reg.${sample_id} \
                    ${sample_id}.tumor.mot.all.tmp.par.reg.${sample_id} \
                    ${params.msmutect_dir}/P_${motif}.csv 8 0.3 0.031 > ${out_prefix}.mut
                    
                tr '\\n' ' ' < ${out_prefix}.mut | awk '{ gsub("@","\\n");print \$0 }' > ${out_prefix}.mut.cln
                sh ${make_maf_sh} ${out_prefix} ${phobos_ref}
                head -n 1 ${out_prefix}.mut.maf_like | awk '{ print \$0"\\tNormal_bases_allele1\\tNormal_bases_allele2\\tTumor_bases_allele1\\tTumor_bases_allele2\\tTumor_bases_allele3\\tTumor_bases_allele4"}' > ${out_prefix}.mut.maf_like.base_change
                sh ${add_base_sh} ${out_prefix}.mut.maf_like ${phobos_ref} ${out_prefix}.mut.maf_like.base_change
                awk 'BEGIN{ FS="\\t" }{ n=n+1;if(n==1){ print \$0 };if(\$2==1){ print \$0 } }' ${out_prefix}.mut.maf_like.base_change > ${out_prefix}.mut.maf_like.dec
                """
}

process mergeAndFinalizeMSI {
    tag "Finalize MSI for ${sample_id}"
    publishDir "${params.somatic_msi_output}/${sample_id}", mode: 'copy', pattern: "*.msmutect.filtered.vcf"
    conda "msmutect2"
    executor 'lsf'
    queue 'short'
    memory '8.GB'
    cpus 1
    time '1h'
    module 'python'
    module 'perl'

    input:
    tuple val(sample_id), val(tumor_id), val(normal_id), path(dec_files)
    path phobos_ref
    path reference_genome

    output:
    tuple val(sample_id), path("*.msmutect.filtered.vcf")

    script:
    def add_coverage_py = "/rsrch8/home/<USER>/vilarsanchez/archive/ndeng/all_neoantigen/workflows/ndeng/add-maf-coverage.py"
    // Please provide correct path
    def maf2vcf_pl = "/rsrch3/home/<USER>/ndeng1/software/vcf2maf-1.6.22/maf2vcf.pl"
    // Please provide correct path

    // Get the first .dec file from the list for the header.
    // Nextflow passes 'dec_files' as a list of Path objects.
    // Sort to ensure a consistent 'first' file if multiple exist and then take the first element.
    def first_dec_file = dec_files.sort().first()

    """
                head -n 1 ${first_dec_file} > ${sample_id}.merged.maf  
                for f in *.dec; do
                awk 'NR>1 { print \$0 }' "\$f" >> ${sample_id}.merged.maf
                done
                python ${add_coverage_py} ${sample_id}.merged.maf ${sample_id}.merged.coverage.maf ${normal_id} ${tumor_id}
                perl ${maf2vcf_pl} --input-maf ${sample_id}.merged.coverage.maf --output-dir . --output-vcf ${sample_id}.msmutect.coverage.vcf --ref-fasta ${reference_genome}
                sample1=\$(grep "#CHROM" ${sample_id}.msmutect.coverage.vcf | cut -f10)
                sample2=\$(grep "#CHROM" ${sample_id}.msmutect.coverage.vcf | cut -f11)
                if [ "\$sample1" == "${tumor_id}" ]; then
                    cp ${sample_id}.msmutect.coverage.vcf ${sample_id}.msmutect.vcf
                else
                    grep "^#" ${sample_id}.msmutect.coverage.vcf | sed "s/\\\$sample1\\t\\\$sample2/\\\$sample2\\t\\\$sample1/" > ${sample_id}.msmutect.vcf.tmp && \\
                    grep -v "^#" ${sample_id}.msmutect.coverage.vcf | awk 'BEGIN{ FS=OFS="\\t"}{ print \$1, \$2, \$3, \$4, \$5, \$6, \$7, \$8, \$9, \$11, \$10 }' >> ${sample_id}.msmutect.vcf.tmp && \\
                    mv ${sample_id}.msmutect.vcf.tmp ${sample_id}.msmutect.filtered.vcf
                fi
                """
}

process combineAndDecomposeSomaticVCFs {
    tag "Combine and Decompose VCFs for ${sample_id}"
    publishDir "${params.final_vcf_output}/${sample_id}", mode: 'copy', pattern: "*.somatic.merged.decomposed.vcf.gz"

    executor 'lsf'
    queue 'short'
    memory '8.GB'
    cpus 1
    time '2h'

    input:
    tuple val(sample_id), val(tumor_id), path(mutect2_vcf), path(mutect2_tbi), path(msmutect_vcf), val(normal_id), path(msmutect_4_vcf), path(msmutect_4_tbi)
    path reference_genome

    output:
    tuple val(sample_id), path("*.somatic.merged.decomposed.vcf.gz")

    script:
    def bcftools = "/rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools"
    // Please provide correct path
    """
                set -e
                
                # Filter Mutect2 VCF to keep only PASS variants
                ${bcftools} view -f PASS ${mutect2_vcf} -Oz -o ${sample_id}.mutect2.PASS.vcf.gz
                ${bcftools} index -t ${sample_id}.mutect2.PASS.vcf.gz

                # Process Mutect2 VCF: Keep only tumor sample and add CALLER info.
                zgrep "^##" ${sample_id}.mutect2.PASS.vcf.gz > ${sample_id}.mutect2.processed.vcf
                echo '##INFO=<ID=CALLER,Number=1,Type=String,Description="CALLER used to get the result">' >> ${sample_id}.mutect2.processed.vcf
                zgrep "^#CHROM" ${sample_id}.mutect2.PASS.vcf.gz >> ${sample_id}.mutect2.processed.vcf
                zgrep -v "^#" ${sample_id}.mutect2.PASS.vcf.gz | awk -v CALLER="mutect2" 'BEGIN{ FS=OFS="\t" }{ if (\$8 == "." || \$8 == "") \$8 = "CALLER=" CALLER; else \$8 = \$8 ";CALLER=" CALLER; print }' >> ${sample_id}.mutect2.processed.vcf
                
                # Sort and then index the filtered Mutect2 VCF
                ${bcftools} view -s ${tumor_id} ${sample_id}.mutect2.processed.vcf | ${bcftools} sort -Oz -o ${sample_id}.mutect2.vcf.gz
                ${bcftools} index -t "${sample_id}.mutect2.vcf.gz"
                rm ${sample_id}.mutect2.processed.vcf
                rm ${sample_id}.mutect2.PASS.vcf.gz*

                # Process MSMuTect VCF: Keep only tumor sample and add CALLER info.
                grep "^##" ${msmutect_vcf} > ${sample_id}.msmutect2.processed.vcf
                echo '##INFO=<ID=CALLER,Number=1,Type=String,Description="CALLER, used to get the result">' >> ${sample_id}.msmutect2.processed.vcf
                grep "^#CHROM" ${msmutect_vcf} >> ${sample_id}.msmutect2.processed.vcf
                grep -v "^#" ${msmutect_vcf} | awk -v CALLER="msmutect" 'BEGIN{ FS=OFS="\t" }{ if (\$8 == "." || \$8 == "") \$8 = "CALLER=" CALLER; else \$8 = \$8 ";CALLER=" CALLER; print }' >> ${sample_id}.msmutect2.processed.vcf
                
                # Sort and then index the filtered MSMuTect VCF
                ${bcftools} view -s ${tumor_id} ${sample_id}.msmutect2.processed.vcf | ${bcftools} sort -Oz -o ${sample_id}.msmutect2.vcf.gz
                ${bcftools} index -t "${sample_id}.msmutect2.vcf.gz"
                rm ${sample_id}.msmutect2.processed.vcf
                
                # Concatenate VCFs using bcftools concat with -a (allow-overlaps)
                # The -a flag helps when chromosome blocks are not strictly contiguous across input files.
                ${bcftools} concat -a ${sample_id}.mutect2.vcf.gz ${sample_id}.msmutect2.vcf.gz ${msmutect_4_vcf} -Oz -o ${sample_id}.somatic.merged.before_clean.vcf.gz 
                ${bcftools} index -t "${sample_id}.somatic.merged.before_clean.vcf.gz"

                # Filter to interval and then decompose
                ${bcftools} view -R ${params.interval_padded} ${sample_id}.somatic.merged.before_clean.vcf.gz -Oz -o ${sample_id}.somatic.merged.vcf.gz
                ${bcftools} index -t "${sample_id}.somatic.merged.vcf.gz"
                
                # Decompose the merged VCF
                ${bcftools} norm -m -any -f ${reference_genome} ${sample_id}.somatic.merged.vcf.gz -Oz -o ${sample_id}.somatic.merged.decomposed.vcf.gz
                ${bcftools} index -t "${sample_id}.somatic.merged.decomposed.vcf.gz"

                """
}


process runMSMuTect4 {
    tag "MSMuTect_4 on ${sample_id}"
    publishDir "${params.msmutect_output}/${sample_id}", mode: 'copy'

    executor 'lsf'
    queue 'medium'
    memory '256.GB'
    cpus 24
    time '24h'

    input:
    tuple val(sample_id), val(tumor_id), path(tumor_bam), path(tumor_bai), path(normal_bams)
    path phobos_ref

    output:
    tuple val(sample_id), path("*.full.mut.tsv"), path("${sample_id}.MsMutect4.filtered.vcf.gz"), path("${sample_id}.MsMutect4.filtered.vcf.gz.tbi"), path("${sample_id}.MsMutect4.trimmed.vcf.gz"), path("${sample_id}.MsMutect4.trimmed.vcf.gz.tbi")

    script:
    def normal_bam_input = normal_bams.size() > 1 ? "merged_normal.bam" : normal_bams[0]
    def merge_command = normal_bams.size() > 1
        ? """
                    samtools merge -@ ${task.cpus - 1} ${normal_bam_input} ${normal_bams.join(' ')}
                    samtools index ${normal_bam_input}
                    """
        : ""

    def bcftools = "/rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools"

    """

                set +u
                ml miniforge3/24.5.0-0
                eval "\$(/risapps/rhel8/miniforge3/24.5.0-0/bin/conda shell.bash hook)"

                conda activate msmutect4
                set -u

                ${merge_command}
                touch ${normal_bam_input}.bai
                touch ${tumor_bam}.bai

                /rsrch3/home/<USER>/ndeng1/software/msmutect4/MSMuTect_4/msmutect.sh \
                    -T ${tumor_bam} \
                    -N ${normal_bam_input} \
                    -l ${phobos_ref} \
                    -O ${sample_id} \
                    -c ${task.cpus} \
                    -m -A -H --vcf

                    
                ${bcftools} view -Oz -o "${sample_id}.MsMutect4.filtered.vcf.gz" "${sample_id}.vcf" 
                ${bcftools} index -t "${sample_id}.MsMutect4.filtered.vcf.gz"

                echo -e "${tumor_id}" > sample_name.txt
                
                /rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools view -f PASS ${sample_id}.vcf \
                | awk 'BEGIN{OFS="\\t"} \
                    /^##contig=<ID=/ { \
                    if (\$0 ~ /ID=1>/) { \
                        gsub(/ID=1>/, "ID=chr1>"); \
                    } else if (\$0 ~ /ID=2>/) { \
                        gsub(/ID=2>/, "ID=chr2>"); \
                    } else if (\$0 ~ /ID=3>/) { \
                        gsub(/ID=3>/, "ID=chr3>"); \
                    } else if (\$0 ~ /ID=4>/) { \
                        gsub(/ID=4>/, "ID=chr4>"); \
                    } else if (\$0 ~ /ID=5>/) { \
                        gsub(/ID=5>/, "ID=chr5>"); \
                    } else if (\$0 ~ /ID=6>/) { \
                        gsub(/ID=6>/, "ID=chr6>"); \
                    } else if (\$0 ~ /ID=7>/) { \
                        gsub(/ID=7>/, "ID=chr7>"); \
                    } else if (\$0 ~ /ID=8>/) { \
                        gsub(/ID=8>/, "ID=chr8>"); \
                    } else if (\$0 ~ /ID=9>/) { \
                        gsub(/ID=9>/, "ID=chr9>"); \
                    } else if (\$0 ~ /ID=10>/) { \
                        gsub(/ID=10>/, "ID=chr10>"); \
                    } else if (\$0 ~ /ID=11>/) { \
                        gsub(/ID=11>/, "ID=chr11>"); \
                    } else if (\$0 ~ /ID=12>/) { \
                        gsub(/ID=12>/, "ID=chr12>"); \
                    } else if (\$0 ~ /ID=13>/) { \
                        gsub(/ID=13>/, "ID=chr13>"); \
                    } else if (\$0 ~ /ID=14>/) { \
                        gsub(/ID=14>/, "ID=chr14>"); \
                    } else if (\$0 ~ /ID=15>/) { \
                        gsub(/ID=15>/, "ID=chr15>"); \
                    } else if (\$0 ~ /ID=16>/) { \
                        gsub(/ID=16>/, "ID=chr16>"); \
                    } else if (\$0 ~ /ID=17>/) { \
                        gsub(/ID=17>/, "ID=chr17>"); \
                    } else if (\$0 ~ /ID=18>/) { \
                        gsub(/ID=18>/, "ID=chr18>"); \
                    } else if (\$0 ~ /ID=19>/) { \
                        gsub(/ID=19>/, "ID=chr19>"); \
                    } else if (\$0 ~ /ID=20>/) { \
                        gsub(/ID=20>/, "ID=chr20>"); \
                    } else if (\$0 ~ /ID=21>/) { \
                        gsub(/ID=21>/, "ID=chr21>"); \
                    } else if (\$0 ~ /ID=22>/) { \
                        gsub(/ID=22>/, "ID=chr22>"); \
                    } else if (\$0 ~ /ID=X>/) { \
                        gsub(/ID=X>/, "ID=chrX>"); \
                    } else if (\$0 ~ /ID=Y>/) { \
                        gsub(/ID=Y>/, "ID=chrY>"); \
                    } \
                    print; \
                    next; \
                    } \
                    /^##/ {print} \
                    /^#CHROM/ { \
                    print "##INFO=<ID=CALLER,Number=1,Type=String,Description=\\"CALLER used to get the result\\">"; \
                    print "##FORMAT=<ID=GT,Number=1,Type=String,Description=\\"Genotype\\">"; \
                    print \$0, "FORMAT", "DUMMY"; \
                    next; \
                    } \
                    !/^#/ { \
                    if (\$8 == "." || \$8 == "") \$8 = "CALLER=MsMutect4"; \
                    else \$8 = \$8 ";CALLER=MsMutect4"; \
                    if (\$1 ~ /^[0-9XY]+\$/) \$1 = "chr" \$1; \
                    print \$0, "GT", "0/1"; \
                    }' \
                | /rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools reheader -s sample_name.txt -o ${sample_id}.MsMutect4.formatted.vcf
                /rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools view -Oz -o ${sample_id}.MsMutect4.trimmed.vcf.gz ${sample_id}.MsMutect4.formatted.vcf
                /rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools index -t ${sample_id}.MsMutect4.trimmed.vcf.gz


                """
}


process runVEPonNormal {

    tag "VEP annotation on Normal"
    publishDir "${params.germline_vcf_output}/", mode: 'copy'
    container '/rsrch3/home/<USER>/ndeng1/software/vep/ensembl-vep_release_114.1.sif'
    // Please provide correct path
    executor 'lsf'
    queue 'medium'
    memory '32.GB'
    cpus 8
    time '24h'

    input:
    tuple val(normal_id), path(vcf), path(tbi)

    output:
    path "*germline.annotated.vep.vcf*"

    script:

    """
                vep --input_file ${vcf} \\
                    --output_file ${normal_id}.germline.annotated.vep.vcf \\
                    --format vcf \\
                    --vcf \\
                    --symbol \\
                    --terms SO \\
                    --tsl \\
                    --hgvs \\
                    --sift p \\
                    --check_existing \\
                    --merged \\
                    --fasta ${params.reference_genome} \\
                    --species ${params.vep_species} \\
                    --assembly ${params.vep_genome_build} \\
                    --dir_plugins ${params.vep_plugin} \\
                    --dir_cache ${params.vep_cache} \\
                    --plugin Downstream \\
                    --plugin Wildtype \\
                    --plugin Frameshift \\
                    --no_progress \\
                    --offline \\
                    --cache \\
                    --exclude_predicted \\
                    --use_transcript_ref \\
                    --pick
                """
}

process runVEP {
    tag "VEP annotation for ${sample_id}"
    publishDir "${params.final_vcf_output}/${sample_id}", mode: 'copy', pattern: "*.vep.vcf"
    container '/rsrch3/home/<USER>/ndeng1/software/vep/ensembl-vep_release_114.1.sif'
    // Please provide correct path
    executor 'lsf'
    queue 'medium'
    memory '32.GB'
    cpus 8
    time '24h'

    input:
    tuple val(sample_id), path(decomposed_vcf)
    path reference_fasta

    output:
    tuple val(sample_id), path("*.vep.vcf")

    script:
    // Human-specific VEP parameters
    def vep_species = "homo_sapiens"
    def vep_genome_build = "GRCh38"
    def vep_plugin = "/rsrch3/home/<USER>/ndeng1/software/vep/pvactools_plugin"
    // Please provide correct path
    def vep_cache = "/rsrch3/home/<USER>/ndeng1/software/vep/vep_data_114_1/"
    // Please provide correct path
    def output_filename = "${sample_id}.annotated.vep.vcf"

    """
                vep --input_file ${decomposed_vcf} \\
                    --output_file ${output_filename} \\
                    --format vcf \\
                    --vcf \\
                    --symbol \\
                    --terms SO \\
                    --tsl \\
                    --hgvs \\
                    --sift p \\
                    --check_existing \\
                    --merged \\
                    --fasta ${reference_fasta} \\
                    --species ${vep_species} \\
                    --assembly ${vep_genome_build} \\
                    --dir_plugins ${vep_plugin} \\
                    --dir_cache ${vep_cache} \\
                    --plugin Downstream \\
                    --plugin Wildtype \\
                    --plugin Frameshift \\
                    --no_progress \\
                    --offline \\
                    --cache \\
                    --exclude_predicted \\
                    --use_transcript_ref \\
                    --pick
                """
}

process runBamReadcountDNA {
    tag "BamReadcount for ${sample_id}"
    // publishDir "${params.bam_readcount_output}/${sample_id}", mode: 'copy'
    container '/rsrch3/home/<USER>/ndeng1/software/bam_readcount_helper/bam-readcount-helper_1.0.0.sif'
    // Please provide correct path

    executor 'lsf'
    queue 'short'
    memory '16.GB'
    cpus 1
    time '3h'

    input:
    tuple val(sample_id), path(vep_vcf), val(tumor_id), path(tumor_bam), path(tumor_bai)
    path reference_fasta

    output:
    tuple val(sample_id), file("${sample_id}_DNA/*")

    script:
    """
                mkdir ${sample_id}_DNA
                python /bam-readcount/build/bin/bam_readcount_helper.py ${vep_vcf} ${tumor_id} ${reference_fasta} ${tumor_bam} ${sample_id}_DNA
                """
}

process runVcfReadcountAnnotator {
    tag "Annotating readcounts for ${sample_id}"
    // publishDir "${params.final_vcf_output}/${sample_id}", mode: 'copy', pattern: "*.readcountDNA.vcf.gz"
    container '/rsrch3/home/<USER>/ndeng1/software/vatools/vatools_5.2.0.sif'
    // Please provide correct path

    executor 'lsf'
    queue 'short'
    memory '8.GB'
    cpus 1
    time '1h'

    input:
    tuple val(sample_id), path(vep_vcf), path(bam_readcount_files)

    output:
    tuple val(sample_id), path("*.readcountDNA.vcf.gz")

    script:
    def snv_readcount = bam_readcount_files.find { it.name.endsWith('_bam_readcount_snv.tsv') }
    def indel_readcount = bam_readcount_files.find { it.name.endsWith('_bam_readcount_indel.tsv') }
    """
                vcf-readcount-annotator \\
                    ${vep_vcf} \\
                    ${snv_readcount} DNA \\
                    -t snv \\
                    -o ${sample_id}.somatic.readcount2.vcf.gz

                vcf-readcount-annotator \\
                    ${sample_id}.somatic.readcount2.vcf.gz \\
                    ${indel_readcount} DNA \\
                    -t indel \\
                    -o ${sample_id}.somatic.readcountDNA.vcf.gz
                """
}

process runBamReadcountRNA {
    tag "RNA BamReadcount for ${sample_id}"
    // publishDir "${params.bam_readcount_output}/${sample_id}", mode: 'copy'
    container '/rsrch3/home/<USER>/ndeng1/software/bam_readcount_helper/bam-readcount-helper_1.0.0.sif'

    input:
    tuple val(sample_id), path(rna_readcount_vcf), val(tumor_id), path(star_bam), path(star_bai)
    path reference_fasta

    output:
    tuple val(sample_id), file("${sample_id}_RNA/*")

    script:
    """
                mkdir ${sample_id}_RNA
                python /bam-readcount/build/bin/bam_readcount_helper.py ${rna_readcount_vcf} ${tumor_id} ${reference_fasta} ${star_bam} ${sample_id}_RNA
                """
}

process runFinalAnnotation {
    tag "Final RNA annotation for ${sample_id}"
    // Now publishing the uncompressed VCF, as gzipping is moved to indexFinalVcf
    publishDir "${params.final_vcf_output}/${sample_id}", mode: 'copy', saveAs: { filename -> filename.endsWith('.vcf') ? filename : null }
    container '/rsrch3/home/<USER>/ndeng1/software/vatools/vatools_5.2.0.sif'
    // Please provide correct path

    executor 'lsf'
    queue 'short'
    memory '8.GB'
    cpus 1
    time '1h'

    input:
    tuple val(sample_id), path(dna_readcount_vcf), path(rna_readcount_files), path(cufflinks_file)

    output:
    tuple val(sample_id), path("*.somatic.vcf")

    script:
    def snv_readcount = rna_readcount_files.find { it.name.endsWith('_bam_readcount_snv.tsv') }
    def indel_readcount = rna_readcount_files.find { it.name.endsWith('_bam_readcount_indel.tsv') }
    """
                vcf-readcount-annotator \\
                    ${dna_readcount_vcf} \\
                    ${snv_readcount} RNA \\
                    -t snv \\
                    -o ${sample_id}.somatic.readcount2.vcf.gz

                vcf-readcount-annotator \\
                    ${sample_id}.somatic.readcount2.vcf.gz \\
                    ${indel_readcount} RNA \\
                    -t indel \\
                    -o ${sample_id}.somatic.readcount.vcf.gz

                vcf-expression-annotator \\
                    -o ${sample_id}.somatic.vcf \\
                    ${sample_id}.somatic.readcount.vcf.gz \\
                    --ignore-ensembl-id-version \\
                    ${cufflinks_file} \\
                    cufflinks \\
                    transcript
                """
}

// NEW PROCESS: Index Final VCF - now also handles gzipping
process indexFinalVcf {
    tag "Gzip and Index final VCF for ${sample_id}"
    publishDir "${params.final_vcf_output}/${sample_id}", mode: 'copy', pattern: "*.somatic.vcf.gz*"
    // Publish both .gz and .gz.tbi
    executor 'lsf'
    queue 'short'
    memory '16.GB'
    cpus 1
    time '3h'

    input:
    tuple val(sample_id), path(uncompressed_vcf)

    output:
    tuple val(sample_id), path("${uncompressed_vcf}.gz"), path("${uncompressed_vcf}.gz.tbi")

    script:
    def bcftools = "/rsrch3/home/<USER>/ndeng1/software/bcftools/bcftools"
    // Please provide correct path
    """
                ${bcftools} view -Oz -o ${uncompressed_vcf}.gz ${uncompressed_vcf}
                ${bcftools} index -t ${uncompressed_vcf}.gz
                """
}


process combineGermlineAndSomaticWithBackphasing {
    tag "Combine VCFs, sort, and perform backphasing on ${sample_id}"
    publishDir "${params.final_vcf_output}/${sample_id}", mode: 'copy', pattern: "*.sorted.phased.vcf.gz*"
    module 'gatk3/3.7'
    module 'openjdk/8u332-b09'
    executor 'lsf'
    queue 'medium'
    memory '32.GB'
    cpus 4
    time '8h'

    input:
    tuple val(sample_id), val(tumor_name), val(normal_names), path(somatic_vcf), path(somatic_tbi), path(germline_vcfs), path(germline_vcfs_tbis), val(tumor_id), path(tumor_bam), path(tumor_bai)

    output:
    tuple val(sample_id), path("${sample_id}.sorted.phased.vcf")

    script:
    def germline_inputs = germline_vcfs
        .withIndex()
        .collect { vcf, idx ->
            "--variant ${vcf}.renamed.vcf.gz"
        }
        .join(' ')
    """
                # Step 1: Rename sample names in somatic VCF
                ${params.bcftools} reheader -n ${sample_id} ${somatic_vcf} > ${somatic_vcf}.renamed.vcf.gz
                zcat ${somatic_vcf}| \\
                sed 's/^##<ID=ID,Number=,Type=/##MsMutect=<ID=ID,Number=,Type=/' | \\
                ${params.bcftools} view -W=tbi -Oz -o ${somatic_vcf}.renamed.vcf.gz

                # Step 2: Rename sample names in germline VCFs
                ${germline_vcfs.withIndex().collect { vcf, idx ->
        """
                    ${params.bcftools} reheader -n ${tumor_name} ${vcf} | ${params.bcftools} view -Oz -o ${vcf}.renamed.vcf.gz 
                    ${params.bcftools} index -t ${vcf}.renamed.vcf.gz
                    """
    }.join('\n    ')}

                # Step 3: Combine germline and somatic VCFs
                java -jar -Xmx8g /risapps/rhel8/gatk3/3.7/GenomeAnalysisTK.jar \\
                    -T CombineVariants \\
                    -R ${params.reference_genome} \\
                    --variant ${somatic_vcf}.renamed.vcf.gz \\
                    ${germline_inputs} \\
                    -o ${sample_id}.combined_somatic_plus_germline.vcf \\
                    --assumeIdenticalSamples

                # Step 4: Sort the combined VCF


                ${params.bcftools} sort -Oz -W=tbi -o ${sample_id}.combined_somatic_plus_germline.sorted.vcf.gz ${sample_id}.combined_somatic_plus_germline.vcf


                # Step 5: Perform read-backed phasing
                java -jar -Xmx16g  /risapps/rhel8/gatk3/3.7/GenomeAnalysisTK.jar \\
                    -T ReadBackedPhasing \\
                    -R ${params.reference_genome} \\
                    -I ${tumor_bam} \\
                    --variant ${sample_id}.combined_somatic_plus_germline.sorted.vcf.gz \\
                    -L ${sample_id}.combined_somatic_plus_germline.sorted.vcf.gz \\
                    -o ${sample_id}.sorted.phased.vcf

                """
}

process runVEPonBackphased {
    tag "VEP annotation on backphased VCF for ${sample_id}"
    publishDir "${params.final_vcf_output}/${sample_id}", mode: 'copy', pattern: "*.backphased.vep.vcf"
    container '/rsrch3/home/<USER>/ndeng1/software/vep/ensembl-vep_release_114.1.sif'
    executor 'lsf'
    queue 'medium'
    memory '32.GB'
    cpus 8
    time '24h'

    input:
    tuple val(sample_id), path(phased_vcf)

    output:
    tuple val(sample_id), path("${sample_id}.backphased.vep.vcf")

    script:
    """
                vep --input_file ${phased_vcf} \\
                    --output_file ${sample_id}.backphased.vep.vcf \\
                    --format vcf \\
                    --vcf \\
                    --symbol \\
                    --terms SO \\
                    --tsl \\
                    --hgvs \\
                    --sift p \\
                    --check_existing \\
                    --merged \\
                    --fasta ${params.reference_genome} \\
                    --species ${params.vep_species} \\
                    --assembly ${params.vep_genome_build} \\
                    --dir_plugins ${params.vep_plugin} \\
                    --dir_cache ${params.vep_cache} \\
                    --plugin Downstream \\
                    --plugin Wildtype \\
                    --plugin Frameshift \\
                    --no_progress \\
                    --offline \\
                    --cache \\
                    --exclude_predicted \\
                    --use_transcript_ref \\
                    --pick
                """
}

// New process to run Pvactools
process runPvactools {
    tag "Running Pvactools for ${sample_id}"
    publishDir "${params.pvactools_output}/", mode: 'copy'
    // Output to the specific directory as requested
    container '/rsrch3/home/<USER>/ndeng1/software/pvactools/pvactools_5.4.1.sif'
    // Please provide correct path
    executor 'lsf'
    queue 'medium'
    memory '64.GB'
    cpus 28
    time '24h'

    input:
    // Combined inputs: somatic VCF (indexed), phased VCF (GATK-indexed)
    tuple val(sample_id), path(somatic_vcf_path), path(somatic_vcf_tbi), path(phased_vcf), path(phlat_file)

    output:
    path "${sample_id}"

    script:
    """

                # Rename sample in VCF and create new file
                ${params.bcftools} reheader -n ${sample_id} ${somatic_vcf_path} | \\
                ${params.bcftools} view -Oz -W=tbi -o ${sample_id}_somatic_renamed.vcf.gz

                ${params.bcftools} reheader -n ${sample_id} ${phased_vcf} | \\
                ${params.bcftools} view -Oz -W=tbi -o ${sample_id}_phased_renamed.vcf.gz

                # Step 2: Extract HLA class I alleles (A, B, C)
                mhci_file="${sample_id}_mhc1.txt"
                sed '1d' ${phlat_file} | egrep "HLA_A|HLA_B|HLA_C" | cut -f1-3 | while read locus a1 a2; do \
                a1=\$(echo \$a1 | cut -d: -f1-2); \
                a2=\$(echo \$a2 | cut -d: -f1-2); \
                [[ -n "\$a1" ]] && echo "HLA-\$a1"; \
                [[ -n "\$a2" ]] && echo "HLA-\$a2"; \
                done | sort -u | tr '\n' ',' | sed 's/,\$//g' > \$mhci_file

                hla1=\$(cat \$mhci_file)

                # Extract HLA class II alleles (DQA1, DQB1, DRB1)
                dqa1=\$(awk '\$1=="HLA_DQA1"{print \$2}' ${phlat_file} | cut -d: -f1-2)
                dqa2=\$(awk '\$1=="HLA_DQA1"{print \$3}' ${phlat_file} | cut -d: -f1-2)
                dqb1=\$(awk '\$1=="HLA_DQB1"{print \$2}' ${phlat_file} | cut -d: -f1-2)
                dqb2=\$(awk '\$1=="HLA_DQB1"{print \$3}' ${phlat_file} | cut -d: -f1-2)
                drb1=\$(awk '\$1=="HLA_DRB1"{print \$2}' ${phlat_file} | cut -d: -f1-2)
                drb2=\$(awk '\$1=="HLA_DRB1"{print \$3}' ${phlat_file} | cut -d: -f1-2)

                hla2="\$dqa1-\$dqb1,\$dqa1-\$dqb2,\$dqa2-\$dqb1,\$dqa2-\$dqb2,\$drb1,\$drb2"

                combined_alleles="\$hla1,\$hla2"

                # Step 3: Run pvacseq with renamed VCF
                pvacseq run \
                    ${sample_id}_somatic_renamed.vcf.gz \
                    ${sample_id} \
                    \$combined_alleles \
                    ${params.pvactools_algorithms} \
                    ${sample_id} \
                    --n-threads ${task.cpus} \
                    --iedb-install-directory /opt/iedb \
                    -p ${sample_id}_phased_renamed.vcf.gz
                """
}

// ====================================================================================
//  PROCESSES: HLA TYPING (OptiType)
// ====================================================================================
process runOptiType {
    tag "OptiType HLA typing for ${sample_id}"
    publishDir "${params.hla_typing_output}/${sample_id}", mode: 'copy'

    executor 'lsf'
    queue 'medium'
    memory '512.GB'
    cpus 28
    time '24h'
    module 'optitype/1.3.5'

    input:
    tuple val(sample_id), path(fastq_files)

    output:
    tuple val(sample_id), path("*")

    script:
    def fastq1 = fastq_files[0]
    def fastq2 = fastq_files[1]
    """
                set +u
                eval "\$(/risapps/rhel8/miniforge3/24.5.0-0/bin/conda shell.bash hook)"
                conda activate optitype-1.3.5
                set -u

                OptiTypePipeline.py -i ${fastq1} ${fastq2} --dna -v -o ${sample_id}_optitype -p ${sample_id}
                """
}

// ====================================================================================
//  PROCESSES: RNA-seq
// ====================================================================================
process runSTAR {
    tag "STAR alignment for ${sample_name}"
    publishDir "${params.rnaseq_bam_output}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '256.GB'
    cpus 28
    time '3h'
    module 'star/2.7.11b'

    input:
    tuple val(sample_name), path(reads)

    output:
    tuple val(sample_name), path("${sample_name}.Aligned.sortedByCoord.out.bam"), path("${sample_name}.Aligned.sortedByCoord.out.bam.bai")

    script:
    def rg_string = "ID:${sample_name} SM:${sample_name} PL:Illumina LB:lib1 PU:unit1"
    """
                STAR --runThreadN ${task.cpus} --genomeDir ${params.star_genome_dir} --readFilesIn ${reads[0]} ${reads[1]} --readFilesCommand zcat --outSAMtype BAM SortedByCoordinate --outSAMattrRGline ${rg_string} --outSAMstrandField intronMotif --outFileNamePrefix "${sample_name}."
                samtools index "${sample_name}.Aligned.sortedByCoord.out.bam"
                """
}

process runCufflinks {
    tag "Cufflinks on ${sample_name}"
    publishDir "${params.cufflinks_output}/${sample_name}", mode: 'copy'

    executor 'lsf'
    queue 'medium'
    memory '256.GB'
    cpus 28
    time '24h'

    input:
    tuple val(sample_name), path(bam)
    path gtf

    output:
    tuple val(sample_name), path("genes.fpkm_tracking")

    script:
    """
                ${params.cufflinks_exe} -p ${task.cpus} -G ${gtf} -o . ${bam}
                """
}

process runPHLAT {
    tag "PHLAT on ${sample_name}"
    publishDir "${params.phlat_output}/${sample_name}", mode: 'copy'
    executor 'lsf'
    queue 'medium'
    memory '16.GB'
    cpus 28
    time '24h'

    input:
    tuple val(sample_name), path(reads)

    output:
    tuple val(sample_name), path("*.sum")

    script:
    """
                set +u

                ml miniforge3/24.5.0-0
                eval "\$(/risapps/rhel8/miniforge3/24.5.0-0/bin/conda shell.bash hook)"

                conda activate phlat
                set -u


                python2.7 -O ${params.phlatdir}/dist/PHLAT.py \\
                -1 ${reads[0]} \\
                -2 ${reads[1]} \\
                -index ${params.phlatdir}/b2folder \\
                -b2url ${params.bowtie2} \\
                -tag ${sample_name} \\
                -e ${params.phlatdir} \\
                -o .

                """
}




// ====================================================================================
// MAIN WORKFLOW
// ====================================================================================
workflow {

    // ======================================================
    // INPUT PREPARATION & METADATA PROCESSING
    // ======================================================

    // Read and process metadata CSV file
    raw_meta_ch = Channel.fromPath(params.metadata_csv)
        .splitCsv(header: true)
        .map { row -> [sample_id: row.Sample, tumor_id: row.Tumor_WEX, normal_id: row.Paired_Normal_WEX, rna_id: row.Tumor_RNAseq] }

    // Group metadata by tumor_id to collect all associated normal_ids
    meta_ch = raw_meta_ch
        .map { row -> tuple(row.tumor_id, row) }
        .groupTuple()
        .map { tumor_id, rows ->
            def sample_id = rows[0].sample_id
            // Assume sample_id and rna_id are consistent for a given tumor
            def rna_id = rows[0].rna_id
            def normal_ids = rows.collect { it.normal_id }.unique()
            // Collect unique normal IDs
            return [sample_id: sample_id, tumor_id: tumor_id, normal_ids: normal_ids, rna_id: rna_id]
        }

    // Prepare input file channels
    wex_samples_ch = Channel.fromList(params.fastq_dir)
        .flatMap { dir ->
            Channel.fromFilePairs("${dir}/**/*_{R1,R2}*.fastq.gz")
        }
        .map { pair_id, files -> tuple(pair_id.split('_')[0], files) }
    rnaseq_samples_ch = Channel.fromFilePairs("${params.rnaseq_fastq_dir}/**/*_{R1,R2}*.fastq.gz").map { pair_id, files -> tuple(pair_id.split('_')[0], files) }

    // Prepare reference and annotation files
    def known_sites_files = [
        params.dbSNP_vcf,
        "${params.dbSNP_vcf}.tbi",
        params.mills_indels_vcf,
        "${params.mills_indels_vcf}.tbi",
        params.known_indels_vcf,
        "${params.known_indels_vcf}.tbi",
    ]
    known_sites_ch = Channel.fromPath(known_sites_files)
        .toList()
        .ifEmpty { exit(1, "Known sites VCF directory is empty") }

    gtf_file_ch = file(params.gtf_annotation)
    phobos_ref_ch = file(params.phobos_ref)
    motif_ch = Channel.of('A', 'C', 'AG', 'AC')


    // Prepare the intevel files
    interval_bed_files_ch = Channel.fromPath("/rsrch3/home/<USER>/vilarsanchez/reference_new/interval/twist/hg38_padded_seperated/*.bed")
        .map { file ->
            def interval_name = file.name.replace('.bed', '')
            tuple(interval_name, file)
        }


    // ======================================================
    // WES PRE-PROCESSING WORKFLOW
    // ======================================================
    // Align reads, mark duplicates, and apply base quality recalibration
    bwaMemAlignment(wex_samples_ch)
    markDuplicatesSpark(bwaMemAlignment.out)
    baseRecalibrator(markDuplicatesSpark.out, known_sites_ch)
    applyBQSR(baseRecalibrator.out)

    // ======================================================
    // RNA-SEQ EXPRESSION WORKFLOW
    // ======================================================
    // Align RNA-seq reads and quantify gene expression
    star_out_ch = runSTAR(rnaseq_samples_ch)
    cufflinks_out_ch = runCufflinks(star_out_ch.map { sample, bam, bai -> tuple(sample, bam) }, gtf_file_ch)

    // ======================================================
    // WES GERMLINE VARIANT CALLING WORKFLOW
    // ======================================================

    // Step 1: Prepare normal samples for germline calling
    normal_ids_ch = meta_ch.flatMap { it.normal_ids }.unique()
    normal_bams_ch = applyBQSR.out.join(normal_ids_ch.map { id -> tuple(id) })

    // Step 2: Run HaplotypeCaller per sample and interval
    haplotype_caller_out_ch = normal_bams_ch
        .combine(interval_bed_files_ch)
        .map { sample_id, bam, bai, interval_name, interval_file ->
            tuple(sample_id, bam, bai, interval_name, interval_file)
        }

    haplotype_caller_results = haplotypeCallerSingle(
        haplotype_caller_out_ch,
        file(params.reference_genome),
        file(params.reference_genome_idx),
        file(params.reference_genome_dict),
    )

    // Step 3: Group GVCFs by interval for GenomicsDBImport
    gvcf_by_interval_ch = haplotype_caller_results
        .map { sample_id, interval_name, gvcf, gvcf_tbi ->
            tuple(interval_name, gvcf, gvcf_tbi)
        }
        .groupTuple()
        .map { interval_name, gvcfs, gvcf_tbis ->
            tuple(interval_name, gvcfs.flatten(), gvcf_tbis.flatten())
        }

    // Step 5: Import GVCFs into GenomicsDB per interval
    genomicsdb_by_interval_ch = gvcf_by_interval_ch.join(interval_bed_files_ch)


    genomicsdb_results = genomicsDBImportGermline(
        genomicsdb_by_interval_ch,
        file(params.reference_genome),
        file(params.reference_genome_dict),
    )

    // Step 5: Genotype GVCFs per interval
    genotype_by_interval_ch = genomicsdb_results.join(interval_bed_files_ch)


    genotype_results = genotypeGVCFs(
        genotype_by_interval_ch,
        file(params.reference_genome),
        file(params.reference_genome_idx),
        file(params.reference_genome_dict),
    )

    // Step 7: Combine all interval VCFs before VQSR
    combined_vcf_ch = combineIntervalVCFs(
        genotype_results.map { it[1] }.collect(),
        genotype_results.map { it[2] }.collect(),
        file(params.reference_genome),
    )

    // Step 8: Apply VQSR for SNPs and INDELs
    filtered_germline_vcf_ch = runVQSR(
        combined_vcf_ch,
        file(params.hapmap_vcf),
        file(params.hapmap_vcf + ".tbi"),
        file(params.omni_vcf),
        file(params.omni_vcf + ".tbi"),
        file(params.one_k_g_vcf_vqsr),
        file(params.one_k_g_vcf_vqsr + ".tbi"),
        file(params.dbSNP_vcf_vqsr),
        file(params.dbSNP_vcf_vqsr + ".tbi"),
        file(params.mills_indels_vcf),
        file(params.mills_indels_vcf + ".tbi"),
        file(params.reference_genome),
        file(params.reference_genome_idx),
        file(params.reference_genome_dict),
    )

    // Step 9: Filter germline VCF by normal samples
    filterGermlineByNormal_input_ch = filtered_germline_vcf_ch
        .combine(normal_ids_ch)
        .map { vcf, tbi, normal_id -> tuple(vcf, tbi, normal_id) }

    filterGermlineByNormal_out = filterGermlineByNormal(filterGermlineByNormal_input_ch)

    // Step 10: Create channel with sample_id, normal_vcfs, and normal_vcf_tbis
    // First, create a mapping from normal_id to sample_id using meta_ch
    normal_to_sample_mapping_ch = meta_ch.flatMap { meta ->
        meta.normal_ids.collect { normal_id ->
            tuple(normal_id, meta.sample_id)
        }
    }

    // Join filterGermlineByNormal output with the mapping to get sample_id
    germline_vcfs_with_sample_ch = filterGermlineByNormal_out
        .combine(normal_to_sample_mapping_ch, by: 0)
        .view()
        .map { normal_id, vcf, tbi, sample_id ->
            tuple(sample_id, normal_id, vcf, tbi)
        }
        .groupTuple()
        .map { sample_id, normal_ids, vcfs, tbis ->
            tuple(sample_id, vcfs, tbis)
        }
        .view()





    // ======================================================
    // PANEL OF NORMALS (PON) GENERATION WORKFLOW
    // ======================================================

    // Step 1: Run Mutect2 on normal samples for PON generation
    normal_mutect2_out_ch = normal_bams_ch
        .combine(interval_bed_files_ch)
        .map { sample_id, bam, bai, interval_name, interval_file ->
            tuple(sample_id, bam, bai, interval_name, interval_file)
        }

    normal_mutect2_results = runMutect2Normal(
        normal_mutect2_out_ch,
        file(params.reference_genome),
        file(params.reference_genome_idx),
        file(params.reference_genome_dict),
    )

    // Step 2: Group normal VCFs by interval for GenomicsDBImport
    normal_vcf_by_interval_ch = normal_mutect2_results
        .map { sample_id, interval_name, vcf, vcf_tbi ->
            tuple(interval_name, vcf, vcf_tbi)
        }
        .groupTuple()
        .map { interval_name, vcfs, vcf_tbis ->
            tuple(interval_name, vcfs.flatten(), vcf_tbis.flatten())
        }

    // Step 3: Import normal VCFs into GenomicsDB per interval
    pon_genomicsdb_by_interval_ch = normal_vcf_by_interval_ch.join(interval_bed_files_ch)


    pon_genomicsdb_results = genomicsDBImportPON(
        pon_genomicsdb_by_interval_ch,
        file(params.reference_genome),
        file(params.reference_genome_idx),
        file(params.reference_genome_dict),
    )

    // Step 4: Create Somatic Panel of Normals per interval
    pon_by_interval_ch = pon_genomicsdb_results.join(interval_bed_files_ch)


    pon_results = createSomaticPanelOfNormals(
        pon_by_interval_ch,
        file(params.reference_genome),
        file(params.reference_genome_idx),
        file(params.reference_genome_dict),
        file(params.germline_resource),
        file(params.germline_resource_idx),
    )

    // Step 5: Gather all interval PON VCFs to create final PON
    combined_pon_vcf_ch = gatherPONVCFs(
        pon_results.map { it[1] }.collect(),
        pon_results.map { it[2] }.collect(),
        file(params.reference_genome),
    )



    // ======================================================
    // WES SOMATIC VARIANT CALLING WORKFLOW
    // ======================================================

    // Step 1: Prepare BAM files for somatic calling
    bqsr_bams_map_ch = applyBQSR.out
        .map { sample_name, bqsr_bam, bqsr_bai -> tuple(sample_name, [bqsr_bam, bqsr_bai]) }
        .toList()
        .map { list_of_tuples -> list_of_tuples.collectEntries() }

    // Step 2: Prepare tumor-normal pairs for somatic calling
    tumor_normal_pairs_ch = meta_ch
        .combine(bqsr_bams_map_ch)
        .map { meta, bams_map ->
            def tumor_bam_info = bams_map[meta.tumor_id] ?: error("Missing processed BAM for tumor ${meta.tumor_id}")

            def normal_names = meta.normal_ids
            def normal_bams = normal_names.collect { normal_id ->
                def normal_bam_info = bams_map[normal_id] ?: error("Missing processed BAM for normal ${normal_id}")
                return normal_bam_info[0]
            }
            def normal_bais = normal_names.collect { normal_id ->
                def normal_bam_info = bams_map[normal_id] ?: error("Missing processed BAM for normal ${normal_id}")
                return normal_bam_info[1]
            }

            return tuple(meta.sample_id, meta.tumor_id, tumor_bam_info[0], tumor_bam_info[1], normal_names, normal_bams, normal_bais)
        }

    // Step 3: Run Mutect2 somatic variant calling per interval
    mutect2_interval_input_ch = tumor_normal_pairs_ch.combine(interval_bed_files_ch)


    mutect2_results_ch = runMutect2(
        mutect2_interval_input_ch,
        file(params.reference_genome),
        file(params.reference_genome_idx),
        file(params.reference_genome_dict),
        combined_pon_vcf_ch,
        file(params.germline_resource),
        file(params.germline_resource_idx),
    )

    // Step 4: Get pileup summaries for contamination analysis
    // Prepare tumor and normal BAMs for pileup summaries
    tumor_bams_for_pileup_ch = tumor_normal_pairs_ch.map { sample_id, tumor_name, tumor_bam, tumor_bai, normal_names, normal_bams, normal_bais ->
        tuple(sample_id, tumor_name, tumor_bam, tumor_bai)
    }

    normal_bams_for_pileup_ch = tumor_normal_pairs_ch.flatMap { sample_id, tumor_name, tumor_bam, tumor_bai, normal_names, normal_bams, normal_bais ->
        normal_names.collect { normal_name ->
            def normal_idx = normal_names.indexOf(normal_name)
            tuple(sample_id, normal_name, normal_bams[normal_idx], normal_bais[normal_idx])
        }
    }

    // Run GetPileupSummaries for tumor samples
    tumor_pileup_ch = getPileupSummaries(
        tumor_bams_for_pileup_ch,
        file(params.germline_resource),
        file(params.germline_resource_idx),
        file(params.reference_genome),
    )



    // Step 5: Calculate contamination
    contamination_input_ch = tumor_pileup_ch
        .map { sample_id, sample_type, pileup -> tuple(sample_id, pileup) }
        .groupTuple()
        .map { sample_id, tumor_pileups ->
            tuple(sample_id, tumor_pileups[0])
        }

    contamination_ch = calculateContamination(
        contamination_input_ch,
        file(params.reference_genome),
    )

    // Step 6: Combine f1r2-tar-gz outputs from all intervals for read orientation model
    // Now also group stats files for each sample
    f1r2_and_stats_by_sample_ch = mutect2_results_ch
        .map { sample_id, interval_name, vcf, tbi, f1r2, stats ->
            tuple(sample_id, interval_name, f1r2, stats)
        }
        .groupTuple()
        .map { sample_id, interval_names, f1r2_files, stats_files ->
            // Sort f1r2 and stats files by interval name to ensure consistent order
            def zipped = [
                interval_names,
                f1r2_files.flatten(),
                stats_files.flatten(),
            ].transpose().sort { a, b ->
                def a_num = a[0].replaceAll(/.*part_(\d+).*/, '$1').toInteger()
                def b_num = b[0].replaceAll(/.*part_(\d+).*/, '$1').toInteger()
                return a_num <=> b_num
            }
            def sorted_f1r2 = zipped.collect { it[1] }
            def sorted_stats = zipped.collect { it[2] }
            tuple(sample_id, sorted_f1r2, sorted_stats)
        }

    read_orientation_model_ch = learnReadOrientationModel(f1r2_and_stats_by_sample_ch)

    // Step 7: Filter Mutect2 calls per interval
    // Use the merged.filtered.stats from learnReadOrientationModel for each sample
    filter_mutect_input_ch = read_orientation_model_ch
        .join(contamination_ch)
        .combine(mutect2_results_ch, by: 0)

    filtered_mutect_results = filterMutectCalls(filter_mutect_input_ch)

    // Step 8: Merge filtered Mutect2 calls by sample
    merged_mutect_input_ch = filtered_mutect_results
        .groupTuple()
        .map { sample_id, vcfs, tbis ->
            tuple(sample_id, vcfs.flatten(), tbis.flatten())
        }

    filtered_mutect_vcfs = mergeFilteredMutectCalls(
        merged_mutect_input_ch,
        file(params.reference_genome),
    )


    // ======================================================
    // MSMuTect AND NEOANTIGEN WORKFLOW
    // ======================================================

    // Step 1: Run MSMuTect for microsatellite mutation detection
    msmutect_input_ch = applyBQSR.out
    runMSMuTect(msmutect_input_ch, phobos_ref_ch)

    // Step 1b: Run MSMuTect4 for microsatellite mutation detection (new)
    msmutect4_input_ch = tumor_normal_pairs_ch.map { sample_id, tumor_name, tumor_bam, tumor_bai, normal_names, normal_bams, normal_bais ->
        tuple(sample_id, tumor_name, tumor_bam, tumor_bai, normal_bams)
    }

    msmutect4_result = runMSMuTect4(msmutect4_input_ch, phobos_ref_ch)




    // Step 2: Prepare MSMuTect files for somatic MSI processing
    msmutect_files_map_ch = runMSMuTect.out
        .map { sample_id, msi_file -> tuple(sample_id, msi_file) }
        .toList()
        .map { list_of_tuples -> list_of_tuples.collectEntries() }

    // Step 3: Process somatic MSI for each motif
    somatic_msi_input_ch = meta_ch
        .combine(motif_ch)
        .combine(msmutect_files_map_ch)
        .map { meta, motif, files ->
            def tumor_mut = files[meta.tumor_id] ?: error("Missing processed BAM for tumor ${meta.tumor_id}")

            def normal_mut = meta.normal_ids.collect { normal_id ->
                def normal_bam_info = files[normal_id] ?: error("Missing processed BAM for normal ${normal_id}")
                return normal_bam_info
            }

            return tuple(meta.sample_id, motif, tumor_mut, normal_mut)
        }

    somatic_msi_results_ch = processSomaticMSI(somatic_msi_input_ch, phobos_ref_ch)

    // Step 4: Merge and finalize MSI results
    meta_for_msi_join = meta_ch.map { meta -> tuple(meta.sample_id, meta) }

    final_msi_input_ch = somatic_msi_results_ch
        .map { sample_id, motif, dec_file -> tuple(sample_id, dec_file) }
        .groupTuple()
        .combine(meta_for_msi_join, by: 0)
        .map { sample_id, dec_files, meta ->
            def first_normal_id = meta.normal_ids[0]
            // Use first normal for downstream steps that require one
            tuple(sample_id, meta.tumor_id, first_normal_id, dec_files)
        }
    final_msi_vcfs = mergeAndFinalizeMSI(final_msi_input_ch, phobos_ref_ch, file(params.reference_genome))

    // ======================================================
    // FINAL VCF MERGING & ANNOTATION WORKFLOW
    // ======================================================

    // Step 1: Combine Mutect2 and MSMuTect VCFs
    meta_for_vcf_join_ch = meta_ch.map { meta -> tuple(meta.sample_id, meta.tumor_id, meta.normal_ids[0]) }

    combine_vcfs_input_ch = filtered_mutect_vcfs
        .join(final_msi_vcfs)
        .join(meta_for_vcf_join_ch, by: 0)
        .join(
            msmutect4_result.map { sample_id, mut_tsv, filtered_vcf, filtered_tbis, trimmed_vcf, trimmed_tbi ->
                tuple(sample_id, trimmed_vcf, trimmed_tbi)
            }
        )
        .map { sample_id, mutect_files, mutect_tbi, msmutect_file, tumor_id, normal_id, msmutect4_vcf, msmutect4_tbi ->
            tuple(sample_id, tumor_id, mutect_files, mutect_tbi, msmutect_file, normal_id, msmutect4_vcf, msmutect4_tbi)
        }

    // Step 2: Decompose and annotate somatic VCFs
    decomposed_vcf_ch = combineAndDecomposeSomaticVCFs(combine_vcfs_input_ch, file(params.reference_genome))
    vep_vcf_ch = runVEP(decomposed_vcf_ch, file(params.reference_genome))

    // Step 3: Add DNA readcount annotations
    tumor_bams_for_readcount_ch = meta_ch
        .combine(bqsr_bams_map_ch)
        .map { meta, bams_map ->
            def tumor_bam_info = bams_map[meta.tumor_id] ?: error("Missing tumor BAM for ${meta.tumor_id}")
            return tuple(meta.sample_id, meta.tumor_id, tumor_bam_info[0], tumor_bam_info[1])
        }

    bam_readcount_input_ch = vep_vcf_ch.join(tumor_bams_for_readcount_ch)
    bam_readcount_output_ch = runBamReadcountDNA(bam_readcount_input_ch, file(params.reference_genome))
    dna_annotated_vcf_ch = runVcfReadcountAnnotator(vep_vcf_ch.join(bam_readcount_output_ch))

    // Step 4: Add RNA readcount annotations (only for samples with TumorRNAseq data)
    rna_to_sample_mapping_ch = meta_ch
        .filter { meta -> meta.rna_id != null && meta.rna_id != "NA" && meta.rna_id != "" }
        .map { meta -> tuple(meta.rna_id, meta.sample_id, meta.tumor_id) }
        .unique()

    // Create channel for samples without RNA data
    samples_without_rna_ch = meta_ch
        .filter { meta -> meta.rna_id == null || meta.rna_id == "NA" || meta.rna_id == "" }
        .map { meta -> meta.sample_id }
        .unique()

    // Process RNA readcounts only for samples with RNA data
    star_bams_for_readcount_ch = rna_to_sample_mapping_ch
        .join(star_out_ch)
        .map { _rna_id, sample_id, tumor_id, star_bam, star_bai -> tuple(sample_id, tumor_id, star_bam, star_bai) }

    rna_readcount_input_ch = dna_annotated_vcf_ch.join(star_bams_for_readcount_ch)
    rna_readcount_output_ch = runBamReadcountRNA(rna_readcount_input_ch, file(params.reference_genome))

    // Step 5: Add expression annotations and finalize (only for samples with RNA data)
    cufflinks_for_annotation_ch = cufflinks_out_ch
        .join(rna_to_sample_mapping_ch)
        .map { _rna_id, cufflinks_file, sample_id, _tumor_id -> tuple(sample_id, cufflinks_file) }

    // Create final annotation input only for samples with RNA data
    final_annotation_input_ch = dna_annotated_vcf_ch
        .join(rna_readcount_output_ch)
        .join(cufflinks_for_annotation_ch)
        .map { sample_id, dna_vcf, rna_readcounts, cufflinks_file ->
            tuple(sample_id, dna_vcf, rna_readcounts, cufflinks_file)
        }

    // Run final annotation only for samples with RNA data
    final_annotated_vcf_ch = runFinalAnnotation(final_annotation_input_ch)

    // For samples without RNA data, use DNA-only annotated VCFs
    dna_only_vcf_ch = dna_annotated_vcf_ch
        .join(samples_without_rna_ch.map { sample_id -> tuple(sample_id, true) }, remainder: true)
        .filter { sample_id, _vcf, has_no_rna -> has_no_rna == true }
        .map { sample_id, vcf, _has_no_rna -> tuple(sample_id, vcf) }

    // Combine RNA-annotated and DNA-only VCFs
    all_final_vcfs_ch = final_annotated_vcf_ch.mix(dna_only_vcf_ch)
    indexed_final_annotated_vcf_ch = indexFinalVcf(all_final_vcfs_ch)


    runVEPonNormal(
        filterGermlineByNormal_out
    )
    // ======================================================
    // BACKPHASING AND NEOANTIGEN PREDICTION WORKFLOW
    // ======================================================

    // Step 1: Prepare input for combined backphasing process
    backphasing_input_ch = tumor_normal_pairs_ch
        .map { sample_id, tumor_name, tumor_bam, tumor_bai, normal_names, normal_bams, normal_bais ->
            tuple(sample_id, tumor_name, normal_names, tumor_name, tumor_bam, tumor_bai)
        }
        .join(indexed_final_annotated_vcf_ch)
        .join(germline_vcfs_with_sample_ch)
        .map { sample_id, tumor_name, normal_names, tumor_id, tumor_bam, tumor_bai, somatic_vcf, somatic_tbi, normal_vcfs, normal_vcf_tbis ->
            tuple(sample_id, tumor_name, normal_names, somatic_vcf, somatic_tbi, normal_vcfs, normal_vcf_tbis, tumor_id, tumor_bam, tumor_bai)
        }

    // Step 1: Run combined process (combine, sort, and backphase)
    phased_and_indexed_vcf_ch = combineGermlineAndSomaticWithBackphasing(
        backphasing_input_ch
    )

    phlat_results = runPHLAT(wex_samples_ch)

    phlat_results_ch = meta_ch
        .map { meta -> tuple(meta.tumor_id, meta.sample_id) }
        .join(phlat_results)
        .map { tumor_id, sample_id, phlat_file ->
            tuple(sample_id, phlat_file)
        }



    // Step 3: Run VEP annotation on backphased VCF
    vep_backphased_vcf_ch = runVEPonBackphased(phased_and_indexed_vcf_ch)

    // Step 4: Run neoantigen prediction with Pvactools using VEP-annotated backphased VCF
    pvactools_input_ch = indexed_final_annotated_vcf_ch
        .join(vep_backphased_vcf_ch)
        .join(phlat_results_ch)
    // Join by sample_id

    runPvactools(pvactools_input_ch)


    // ======================================================
    // MSISENSOR MICROSATELLITE INSTABILITY WORKFLOW
    // ======================================================

    // Step 1: Prepare tumor-normal pairs for MSIsensor analysis
    msisensor_input_ch = tumor_normal_pairs_ch.map { sample_id, tumor_name, tumor_bam, tumor_bai, normal_names, normal_bams, normal_bais ->
        tuple(sample_id, tumor_name, tumor_bam, tumor_bai, normal_bams)
    }

    // Step 2: Run MSIsensor for microsatellite instability detection
    runMSIsensor(msisensor_input_ch)



    // ======================================================
    // HLA TYPING WORKFLOW
    // ======================================================

    // Run OptiType for HLA typing on WES samples
    // optitype_results = runOptiType(wex_samples_ch)


    // ======================================================
    // QUALITY CONTROL WORKFLOW
    // ======================================================

    // Step 1: Run FastQC on all input files
    wex_fastqc_in = wex_samples_ch.flatMap { sample_name, files -> files.collect { fastq_file -> tuple(sample_name, 'WES', fastq_file) } }
    rnaseq_fastqc_in = rnaseq_samples_ch.flatMap { sample_name, files -> files.collect { fastq_file -> tuple(sample_name, 'RNA-seq', fastq_file) } }

    all_fastqc_in = wex_fastqc_in.mix(rnaseq_fastqc_in)
    runFastQC(all_fastqc_in)

    // Step 2: Generate MultiQC reports
    multiqc_input_ch = runFastQC.out
        .map { sample_name, data_type, qc_file -> tuple(data_type, qc_file) }
        .groupTuple()
        .map { data_type, qc_files ->
            def output_dir = data_type == 'WES' ? params.multiqc_wex_output : params.multiqc_rnaseq_output
            tuple(data_type, output_dir, qc_files.flatten())
        }
    runMultiQC(multiqc_input_ch)
}
