/*
 * agogo.css_t
 * ~~~~~~~~~~~
 *
 * Sphinx stylesheet -- agogo theme.
 *
 * :copyright: Copyright 2007-2011 by the Sphinx team, see AUTHORS.
 * :license: BSD, see LICENSE for details.
 *
 */

* {
  margin: 0px;
  padding: 0px;
}

body {
  font-family: "Verdana", Arial, sans-serif;
  line-height: 1.4em;
  color: black;
  background-color: #eeeeec;
}


/* Page layout */

div.header, div.content, div.footer {
  width: 70em;
  margin-left: auto;
  margin-right: auto;
}

div.header-wrapper {
  background: url(bgtop.png) top left repeat-x;
  border-bottom: 3px solid #2e3436;
}


/* Default body styles */
a {
  color: #ce5c00;
}

div.bodywrapper a, div.footer a {
  text-decoration: underline;
}

.clearer {
  clear: both;
}

.left {
  float: left;
}

.right {
  float: right;
}

.line-block {
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
}

.line-block .line-block {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 1.5em;
}

h1, h2, h3, h4 {
  font-family: "Georgia", "Times New Roman", serif;
  font-weight: normal;
  color: #3465a4;
  margin-bottom: .8em;
}

h1 {
  color: #204a87;
}

h2 {
  padding-bottom: .5em;
  border-bottom: 1px solid #3465a4;
}

a.headerlink {
  visibility: hidden;
  color: #dddddd;
  padding-left: .3em;
}

h1:hover > a.headerlink,
h2:hover > a.headerlink,
h3:hover > a.headerlink,
h4:hover > a.headerlink,
h5:hover > a.headerlink,
h6:hover > a.headerlink,
dt:hover > a.headerlink {
  visibility: visible;
}

img {
  border: 0;
}

div.admonition {
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 2px 7px 1px 7px;
  border-left: 0.2em solid black;
}

p.admonition-title {
  margin: 0px 10px 5px 0px;
  font-weight: bold;
}

dt:target, .highlighted {
  background-color: #fbe54e;
}

/* Header */

div.header {
  padding-top: 10px;
  padding-bottom: 10px;
}

div.header .headertitle {
  font-family: "Georgia", "Times New Roman", serif;
  font-weight: normal;
  font-size: 180%;
  letter-spacing: .08em;
  margin-bottom: .8em;
}

div.header .headertitle a {
  color: white;
}

div.header div.rel {
  margin-top: 1em;
}

div.header div.rel a {
  color: #fcaf3e;
  letter-spacing: .1em;
  text-transform: uppercase;
}

p.logo {
    float: right;
}

img.logo {
    border: 0;
}


/* Content */
div.content-wrapper {
  background-color: white;
  padding-top: 20px;
  padding-bottom: 20px;
}

div.document {
  width: 50em;
  float: left;
}

div.body {
  padding-right: 2em;
  text-align: justify;
}

div.document h1 {
  line-height: 120%;
}

div.document ul {
  margin: 1.5em;
  list-style-type: square;
}

div.document dd {
  margin-left: 1.2em;
  margin-top: .4em;
  margin-bottom: 1em;
}

div.document .section {
  margin-top: 1.7em;
}
div.document .section:first-child {
  margin-top: 0px;
}

div.document div.highlight {
  padding: 3px;
  background-color: #eeeeec;
  border-top: 2px solid #dddddd;
  border-bottom: 2px solid #dddddd;
  margin-top: .8em;
  margin-bottom: .8em;
}

div.document h2 {
  margin-top: .7em;
}

div.document p {
  margin-bottom: .5em;
}

div.document li.toctree-l1 {
  margin-bottom: 1em;
}

div.document .descname {
  font-weight: bold;
}

div.document .docutils.literal {
  background-color: #eeeeec;
  padding: 1px;
}

div.document .docutils.xref.literal {
  background-color: transparent;
  padding: 0px;
}

div.document blockquote {
  margin: 1em;
}

div.document ol {
  margin: 1.5em;
}


/* Sidebar */

div.sidebar {
  width: 20em;
  float: right;
  font-size: .9em;
}

div.sidebar a, div.header a {
  text-decoration: none;
}

div.sidebar a:hover, div.header a:hover {
  text-decoration: underline;
}

div.sidebar h3 {
  color: #2e3436;
  text-transform: uppercase;
  font-size: 130%;
  letter-spacing: .1em;
}

div.sidebar ul {
  list-style-type: none;
}

div.sidebar li.toctree-l1 a {
  display: block;
  padding: 1px;
  border: 1px solid #dddddd;
  background-color: #eeeeec;
  margin-bottom: .4em;
  padding-left: 3px;
  color: #2e3436;
}

div.sidebar li.toctree-l2 a {
  background-color: transparent;
  border: none;
  margin-left: 1em;
  border-bottom: 1px solid #dddddd;
}

div.sidebar li.toctree-l3 a {
  background-color: transparent;
  border: none;
  margin-left: 2em;
  border-bottom: 1px solid #dddddd;
}

div.sidebar li.toctree-l2:last-child a {
  border-bottom: none;
}

div.sidebar li.toctree-l1.current a {
  border-right: 5px solid #fcaf3e;
}

div.sidebar li.toctree-l1.current li.toctree-l2 a {
  border-right: none;
}

div.sidebar input[type="text"] {
  width: 170px;
}

div.sidebar input[type="submit"] {
  width: 30px;
}


/* Footer */

div.footer-wrapper {
  background: url(bgfooter.png) top left repeat-x;
  border-top: 4px solid #babdb6;
  padding-top: 10px;
  padding-bottom: 10px;
  min-height: 80px;
}

div.footer, div.footer a {
  color: #888a85;
}

div.footer .right {
  text-align: right;
}

div.footer .left {
  text-transform: uppercase;
}


/* Styles copied from basic theme */

img.align-left, .figure.align-left, object.align-left {
    clear: left;
    float: left;
    margin-right: 1em;
}

img.align-right, .figure.align-right, object.align-right {
    clear: right;
    float: right;
    margin-left: 1em;
}

img.align-center, .figure.align-center, object.align-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

img.align-center-qualimap {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 100%
}

.align-left {
    text-align: left;
}

.align-center {
    text-align: center;
}

.align-right {
    text-align: right;
}

/* -- search page ----------------------------------------------------------- */

ul.search {
    margin: 10px 0 0 20px;
    padding: 0;
}

ul.search li {
    padding: 5px 0 5px 20px;
    background-image: url(file.png);
    background-repeat: no-repeat;
    background-position: 0 7px;
}

ul.search li a {
    font-weight: bold;
}

ul.search li div.context {
    color: #888;
    margin: 2px 0 0 30px;
    text-align: left;
}

ul.keywordmatches li.goodmatch a {
    font-weight: bold;
}

/* -- index page ------------------------------------------------------------ */

table.contentstable {
    width: 90%;
}

table.contentstable p.biglink {
    line-height: 150%;
}

a.biglink {
    font-size: 1.3em;
}

span.linkdescr {
    font-style: italic;
    padding-top: 5px;
    font-size: 90%;
}

/* -- general index --------------------------------------------------------- */

table.indextable td {
    text-align: left;
    vertical-align: top;
}

table.indextable dl, table.indextable dd {
    margin-top: 0;
    margin-bottom: 0;
}

table.indextable tr.pcap {
    height: 10px;
}

table.indextable tr.cap {
    margin-top: 10px;
    background-color: #f2f2f2;
}

img.toggler {
    margin-right: 3px;
    margin-top: 3px;
    cursor: pointer;
}

/* -- viewcode extension ---------------------------------------------------- */

.viewcode-link {
    float: right;
}

.viewcode-back {
    float: right;
    font-family:: "Verdana", Arial, sans-serif;
}

div.viewcode-block:target {
    margin: -1px -3px;
    padding: 0 3px;
    background-color: #f4debf;
    border-top: 1px solid #ac9;
    border-bottom: 1px solid #ac9;
}
