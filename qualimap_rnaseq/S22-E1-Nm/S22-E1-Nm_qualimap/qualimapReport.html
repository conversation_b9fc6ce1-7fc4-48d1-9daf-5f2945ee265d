<!DOCTYPE HTML>
<html>
	<head>
		<link rel="stylesheet" href="css/agogo.css" type="text/css" />
		<link rel="stylesheet" href="css/report.css" type="text/css" />
	<title>Qualimap report: RNA Seq QC</title>

	</head>
<body>
	<div class="header-wrapper">
		<div class="header">
		<p class="logo"><a href="http://qualimap.bioinfo.cipf.es/">
		<img class="logo" src="css/qualimap_logo_small.png" alt="Logo"/>
		</a></p>
<div class="headertitle"><a href="">Qualimap Report: RNA Seq QC</a></div>
	</div>
</div>

<div class="content-wrapper">
<div class="content">
<div class="document">
<div class="documentwrapper">
<div class="bodywrapper">
<div class="body">
<div class=section>
<h2>Input data and parameters<a class="headerlink" name="input" title="Permalink to this headline">&nbsp;</a></h2>
<div class=summary>


<div class=table-summary>
<h3>Input</h3>
<table class="summary hovertable">
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Analysis date: </td>
<td class=column2>Tue Jul 22 21:02:03 CDT 2025</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>BAM file: </td>
<td class=column2>S22-E1-Nm.STAR.genome.bam</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Counting algorithm:</td>
<td class=column2>uniquely-mapped-reads</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>GTF file: </td>
<td class=column2>/home/<USER>/vilarsanchez/reference_new/star_rsem/GRCh38_ucsc/hg38.ncbiRefSeq.gtf</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Number of bases for 5'-3' bias computation:</td>
<td class=column2>100</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Number of transcripts for 5'-3' bias computation:</td>
<td class=column2>1,000</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Paired-end sequencing:</td>
<td class=column2>no</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Protocol: </td>
<td class=column2>non-strand-specific</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Sorting performed:</td>
<td class=column2>no</td>
</tr>
</table>
</div>

</div>
</div> <!-- summary section -->

<div class=section>
<h2>Summary<a class="headerlink" name="summary" title="Permalink to this headline">&nbsp;</a></h2>
<div class=summary>


<div class=table-summary>
<h3>Reads alignment</h3>
<table class="summary hovertable">
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Number of mapped reads:</td>
<td class=column2>72,093,034</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Total number of alignments:</td>
<td class=column2>128,120,566</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Number of secondary alignments:</td>
<td class=column2>56,027,532</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Number of non-unique alignments:</td>
<td class=column2>66,906,004</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Aligned to genes:</td>
<td class=column2>16,033,105</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Ambiguous alignments:</td>
<td class=column2>1,218,023</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>No feature assigned:</td>
<td class=column2>43,922,110</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Missing chromosome in annotation: </td>
<td class=column2>41,324</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Not aligned:</td>
<td class=column2>27,367,256</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Strand specificity estimation (fwd/rev):</td>
<td class=column2>0.08 / 0.92</td>
</tr>
</table>
</div>


<div class=table-summary>
<h3>Reads genomic origin</h3>
<table class="summary hovertable">
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Exonic: </td>
<td class=column2>16,033,105 / 26.74%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Intronic: </td>
<td class=column2>39,795,937 / 66.38%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Intergenic: </td>
<td class=column2>4,126,173 / 6.88%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Intronic/intergenic overlapping exon: </td>
<td class=column2>2,957,541 / 4.93%</td>
</tr>
</table>
</div>


<div class=table-summary>
<h3>Transcript coverage profile</h3>
<table class="summary hovertable">
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>5' bias:</td>
<td class=column2>0.39</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>3' bias:</td>
<td class=column2>0.37</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>5'-3' bias:</td>
<td class=column2>1.22</td>
</tr>
</table>
</div>


<div class=table-summary>
<h3>Junction analysis</h3>
<table class="summary hovertable">
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>Reads at junctions:</td>
<td class=column2>3,725,100</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>ACCT</td>
<td class=column2>5.17%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>AGGT</td>
<td class=column2>4.77%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>AGGA</td>
<td class=column2>3.31%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>ATCT</td>
<td class=column2>3.18%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>TCCT</td>
<td class=column2>3.13%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>AGCT</td>
<td class=column2>2.99%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>GCCT</td>
<td class=column2>2.6%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>AGAT</td>
<td class=column2>2.55%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>AGGC</td>
<td class=column2>2.41%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>CCCT</td>
<td class=column2>2.22%</td>
</tr>
<tr onmouseover="this.style.backgroundColor='#EEEEEC';" onmouseout="this.style.backgroundColor='#FFFFFF';">
<td class=column1>AGGG</td>
<td class=column2>2.12%</td>
</tr>
</table>
</div>

</div>
</div> <!-- summary section -->



<div class=section>

<h2>Reads Genomic Origin<a class="headerlink" name="Reads Genomic Origin" title="Permalink to this headline">&nbsp;</a></h2>

<div><img width=100% src="images_qualimapReport/Reads Genomic Origin.png"></div>

</div><!-- graph section -->


<div class=section>

<h2>Coverage Profile Along Genes (Total)<a class="headerlink" name="Coverage Profile Along Genes (Total)" title="Permalink to this headline">&nbsp;</a></h2>

<div><img width=100% src="images_qualimapReport/Coverage Profile Along Genes (Total).png"></div>

</div><!-- graph section -->


<div class=section>

<h2>Coverage Profile Along Genes (Low)<a class="headerlink" name="Coverage Profile Along Genes (Low)" title="Permalink to this headline">&nbsp;</a></h2>

<div><img width=100% src="images_qualimapReport/Coverage Profile Along Genes (Low).png"></div>

</div><!-- graph section -->


<div class=section>

<h2>Coverage Profile Along Genes (High)<a class="headerlink" name="Coverage Profile Along Genes (High)" title="Permalink to this headline">&nbsp;</a></h2>

<div><img width=100% src="images_qualimapReport/Coverage Profile Along Genes (High).png"></div>

</div><!-- graph section -->


<div class=section>

<h2>Coverage Histogram (0-50X)<a class="headerlink" name="Transcript coverage histogram" title="Permalink to this headline">&nbsp;</a></h2>

<div><img width=100% src="images_qualimapReport/Transcript coverage histogram.png"></div>

</div><!-- graph section -->


<div class=section>

<h2>Junction Analysis<a class="headerlink" name="Junction Analysis" title="Permalink to this headline">&nbsp;</a></h2>

<div><img width=100% src="images_qualimapReport/Junction Analysis.png"></div>

</div><!-- graph section -->


</div>

</div>

</div>

</div>


<div class="sidebar">
<h3>Contents</h3>
<li class="toctree-l1"><a class="reference internal" href="#input">Input data & parameters</a></li>
<li class="toctree-l1"><a class="reference internal" href="#summary">Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="#Reads Genomic Origin">Reads Genomic Origin</a></li>
<li class="toctree-l1"><a class="reference internal" href="#Coverage Profile Along Genes (Total)">Coverage Profile Along Genes (Total)</a></li>
<li class="toctree-l1"><a class="reference internal" href="#Coverage Profile Along Genes (Low)">Coverage Profile Along Genes (Low)</a></li>
<li class="toctree-l1"><a class="reference internal" href="#Coverage Profile Along Genes (High)">Coverage Profile Along Genes (High)</a></li>
<li class="toctree-l1"><a class="reference internal" href="#Transcript coverage histogram">Coverage Histogram (0-50X)</a></li>
<li class="toctree-l1"><a class="reference internal" href="#Junction Analysis">Junction Analysis</a></li>
</div> <!-- sidebar -->

<div class="clearer"></div>
</div>

</div>


<div class="footer-wrapper">
<div class="footer">
<div class="left">
<div class="footer">
2025/07/22 21:02:06
</div>
</div>
<div class="right">
<div class="footer">
Generated by QualiMap v.2.2.2-dev
</div
</div>
<div class="clearer"></div>
</div> <!-- footer -->
</div>
</body>
