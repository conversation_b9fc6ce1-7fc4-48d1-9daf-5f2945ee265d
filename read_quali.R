# -----------------------------------------------------------------------------
# R Function to Parse Qualimap HTML Report
#
# Description:
# This script defines a function, `read_qual`, that uses the 'rvest' package 
# to read and parse an HTML report generated by Qualimap. It extracts key 
# data points and tables, then organizes them into a structured list, 
# including nested lists and a data frame for chromosome statistics.
#
# Requirements:
# - The 'rvest' package must be installed.
#
# Author: Gemini
# Date: July 8, 2025
# -----------------------------------------------------------------------------

# Install and load the rvest package if it's not already installed
if (!requireNamespace("rvest", quietly = TRUE)) {
  install.packages("rvest")
}
library(rvest)
library(tidyverse)

#' Read and Parse a Qualimap HTML Report
#'
#' This function takes the file path of a Qualimap HTML report, parses its
#' content, and returns a structured list containing the summary statistics
#' and parameters from the report.
#'
#' @param filename A string specifying the path to the Qualimap HTML file.
#' @return A list containing the parsed data, including input parameters,
#'   summary statistics, and chromosome-level stats.
#' @examples
#' \dontrun{
#'   qual_data <- read_qual("path/to/your/qualimapReport.html")
#'   print(qual_data$summary$globals)
#' }
read_qual <- function(filename) {
  
  # --- File Validation ---
  if (!file.exists(filename)) {
    stop("Error: The file '", filename, "' was not found. Please check the path.")
  }
  
  # --- Read the HTML File ---
  report <- read_html(filename)
  
  # --- Helper Function to Extract and Clean Table Data ---
  extract_table_data <- function(html_doc, table_title) {
    nodes <- html_doc %>%
      html_nodes(xpath = paste0("//h3[text()='", table_title, "']/following-sibling::table[1]")) %>%
      html_table(fill = TRUE)
    
    if (length(nodes) > 0) {
      df <- nodes[[1]]
      data_list <- as.list(setNames(df[[2]], gsub(":", "", df[[1]])))
      names(data_list) <- make.names(gsub("\\s+$", "", names(data_list)))
      return(data_list)
    }
    return(list()) # Return an empty list if the table is not found
  }
  
  # --- Main List to Store All Extracted Data ---
  qualimap_data <- list()
  
  # ----------------------------------------------------------------
  # Section 1: Input Data and Parameters
  # ----------------------------------------------------------------
  
  qualimap_data$input_parameters <- list()
  
  qualimap_data$input_parameters$qualimap_command_line <- report %>%
    html_node(xpath = "//h3[text()='QualiMap command line']/following-sibling::table[1]") %>%
    html_node("td") %>%
    html_text(trim = TRUE)
  
  qualimap_data$input_parameters$alignment <- extract_table_data(report, "Alignment")
  qualimap_data$input_parameters$gff_region <- extract_table_data(report, "GFF region")
  
  # ----------------------------------------------------------------
  # Section 2: Summary Statistics
  # ----------------------------------------------------------------
  
  qualimap_data$summary <- list()
  
  qualimap_data$summary$globals <- extract_table_data(report, "Globals")
  qualimap_data$summary$globals_inside_regions <- extract_table_data(report, "Globals (inside of regions)")
  qualimap_data$summary$acgt_content_inside_regions <- extract_table_data(report, "ACGT Content (inside of regions)")
  qualimap_data$summary$coverage_inside_regions <- extract_table_data(report, "Coverage (inside of regions)")
  qualimap_data$summary$mapping_quality_inside_regions <- extract_table_data(report, "Mapping Quality (inside of regions)")
  qualimap_data$summary$insert_size_inside_regions <- extract_table_data(report, "Insert size (inside of regions)")
  qualimap_data$summary$mismatches_indels_inside_regions <- extract_table_data(report, "Mismatches and indels (inside of regions)")
  
  # ----------------------------------------------------------------
  # Section 3: Chromosome Statistics
  # ----------------------------------------------------------------
  
  chromosome_stats_table <- report %>%
    html_node(xpath = "//h3[text()='Chromosome stats (inside of regions)']/following-sibling::table[1]") %>%
    html_table(header = TRUE)
  
  if (!is.null(chromosome_stats_table)) {
    colnames(chromosome_stats_table) <- gsub(" ", "_", colnames(chromosome_stats_table))
    
    numeric_cols <- sapply(chromosome_stats_table, is.numeric)
    
    # Ensure there's at least one numeric column before filtering
    if(any(numeric_cols)) {
        filtered_stats <- chromosome_stats_table[rowSums(chromosome_stats_table[, numeric_cols, drop=FALSE] != 0, na.rm = TRUE) > 0, ]
        qualimap_data$chromosome_stats <- filtered_stats
    } else {
        qualimap_data$chromosome_stats <- chromosome_stats_table
    }

  } else {
    qualimap_data$chromosome_stats <- data.frame() # Return empty data frame if table not found
  }
  
  # Return the final structured list
  return(qualimap_data)
}

# ------------------------------------------------------------------
# Example Usage
# ------------------------------------------------------------------

# Define the path to your report file.
# IMPORTANT: Make sure this path is correct.
html_file_path <- "qualimapReport.html"

# Call the function with the file path
# The 'tryCatch' block will gracefully handle the error if the file doesn't exist.
qual_data <- tryCatch({
  read_qual(html_file_path)
}, error = function(e) {
  message(e)
  return(NULL)
})

# If the data was loaded successfully, display its structure
if (!is.null(qual_data)) {
  cat("--- Qualimap Data Structure ---\n\n")
  str(qual_data, list.len = 4)
}



x = dir("./qualimap_reports", pattern = "Report.html",full.names = TRUE,recursive = TRUE)%>%map(\(x){
    y = read_qual(x)
    y[["sample"]] = x%>%str_extract("(?<=qualimap_reports/).*(?=/qualimapReport.html)")
    y})



x%>%map(function(x){
    x$summary$globals_inside_regions%>%
    as_tibble()%>%
    mutate(sample=x$sample)

})%>%bind_rows()%>%
    separate(Mapped.reads,into=c("Count","percentage"),sep=" /  ")%>%
    mutate(percentage=as.numeric(gsub("%","",percentage)))%>%
    ggbarplot(x="sample",y="percentage",ylim=c(0,100),fill = "sample",ylab = "Mapped Percentage")
library(ggpubr)


# ------------------------------------------------------------------
x%>%map(function(x){
    x$summary$coverage_inside_regions%>%
    as_tibble()%>%
    mutate(sample=x$sample)

})%>%bind_rows()%>%
    ggbarplot(x="sample",y="Mean",fill = "sample",ylab = "Mean Coverage")+
    rotate_x_text()


library(ggpubr)

x%>%map(function(x){
    x$summary$mapping_quality_inside_regions$Mean.Mapping.Quality%>%
    as_tibble()%>%
    mutate(sample=x$sample)

})%>%bind_rows()%>%
    
    
    ggbarplot(x="sample",y="value",fill = "sample",ylab = "Mean Mapping Qual")+
    rotate_x_text()+
    geom_hline(yintercept = 40)


x%>%map(function(x){
    x$summary$acgt_content_inside_regions$GC.Percentage%>%
    as_tibble()%>%
    mutate(sample=x$sample)

})%>%bind_rows()%>%
mutate(value = str_remove_all(value,"%")%>%as.numeric)%>%
    
    
    ggbarplot(x="sample",y="value",fill = "sample",ylab = "GC%")+
    rotate_x_text()+
    geom_hline(yintercept = 48)



