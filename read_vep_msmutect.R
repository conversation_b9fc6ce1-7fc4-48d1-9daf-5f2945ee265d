# Load required packages and functions
library(tidyverse)
library(ggpubr)

# Source the VCF parsing function
source("function.R")



# Call the function with the file path
parsed_csq_tibble <- parse_vcf_csq("./vep_annotated_msmutect/PT_103687_B1_Ade_W68/PT_103687_B1_Ade_W68.annotated.vep.vcf")

# View the result
msmutect <- dir("./vep_annotated_msmutect", recursive = T, full.names = T) %>%
  map_df(
    \(x){
      parse_vcf_csq(x) %>%
        mutate(sample = basename(x) %>% str_remove(".annotated.vep.vcf"))
    }
  )


msmutect %>%
  count(sample, IMPACT) %>%
  complete(sample, IMPACT, fill = list(n = 0)) %>%
  ggbarplot(
    x = "sample",
    y = "n",
    fill = "IMPACT",
    order = c("PT_103687_E1_Ade_BL", "PT_103687_I1_Ade_BL", "PT_103687_B1_Ade_W68", "PT_103690_B1_Ade_BL")
  ) +
  rotate_y_text()


msmutect %>%
  count(sample, IMPACT) %>%
  complete(sample, IMPACT, fill = list(n = 0)) %>%
  filter(IMPACT != "MODIFIER") %>%
  ggbarplot(
    x = "sample",
    y = "n",
    fill = "IMPACT",
    order = c("PT_103687_E1_Ade_BL", "PT_103687_I1_Ade_BL", "PT_103687_B1_Ade_W68", "PT_103690_B1_Ade_BL")
  ) +
  rotate_x_text()


msmutect %>%
  separate(variant_id, into = c("chr", "loci", "ref", "alt")) %>%
  filter(IMPACT %in% c("HIGH", "MOERATE")) %>%
  pivot_wider(
    id_cols = c(chr, loci, SYMBOL),
    names_from = sample,
    values_from = Consequence,
    values_fn = ~ unique(.)
  ) %>%
  unite(SYMBOL, SYMBOL, chr, loci) %>%
  group_by(SYMBOL) %>%
  column_to_rownames("SYMBOL") %>%
  `[`(c("PT_103687_I1_Ade_BL", "PT_103687_B1_Ade_W68", "PT_103690_B1_Ade_BL")) %>%
  ComplexHeatmap::Heatmap(
    bottom_annotation = ComplexHeatmap::HeatmapAnnotation(
      patient = c("103687", "103687", "103690")
    )
  )




library(VennDiagram)

fs_genes <- msmutect %>%
  filter(IMPACT %in% c("HIGH", "MOERATE")) %>%
  pull(SYMBOL) %>%
  unique()
read_tsv("../new_neoantigen/toplist.tsv") %>%
  pull(`Gene Name`) %>%
  unique() -> Ana_100

venn.plot <- draw.pairwise.venn(
  area1 = length(fs_genes),
  area2 = length(Ana_100),
  cross.area = length(intersect(fs_genes %>% str_to_upper(), Ana_100)),
  category = c("nouscom", "Ana\ntop100"),
  fill = c("skyblue", "salmon"),
  lty = "blank"
)

pdf("venn_fs_vs_ana100.pdf", height = 5, width = 5)
grid.draw(venn.plot)
dev.off()



library(ggpubr) # For ggbarplot
library(ggplot2) # Optional, for theme tweaks

# Your data
df <- data.frame(
  Sample = c("PT_103687_E1_Ade_BL", "PT_103690_B1_Ade_BL", "PT_103687_B1_Ade_W68", "PT_103687_I1_Ade_BL"),
  Value = c(50.61, 45.01, 29.85, 43.52)
)

ggbarplot(df,
  x = "Sample", y = "Value",
  fill = "Sample",
  order = c("PT_103687_E1_Ade_BL", "PT_103687_I1_Ade_BL", "PT_103687_B1_Ade_W68", "PT_103690_B1_Ade_BL"),
  ylab = "MSI-score",
  xlab = "Sample"
) +
  rotate_x_text() +
  geom_hline(yintercept = 10) +
  geom_hline(yintercept = 20)
