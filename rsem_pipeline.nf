#!/usr/bin/env nextflow

// ====================================================================================
// RSEM QUANTIFICATION PIPELINE
// ====================================================================================

// Parameters
params.fastq_dir = "/rsrch8/home/<USER>/vilarsanchez/data/KS_TotaRNA-Seq_R1239"
params.star_rsem = "/rsrch3/home/<USER>/vilarsanchez/reference_new/star_rsem/GRCh38_gencode/GRCh38"


// Output directories
params.rsem_output = "result/rsem_counts"
params.qualimap_output = "result/qualimap_rnaseq"

// Optional parameters
params.help = false
params.calc_ci = false
// Set to true to calculate credibility intervals separately

// Help message
def helpMessage() {
    log.info(
        """
    RSEM Quantification Pipeline
    ============================
    
    Usage:
        nextflow run rsem_pipeline.nf [options]
    
    Required Parameters:
        --fastq_dir         Path to directory containing RNA-seq FASTQ files
        --star_rsem         Path to STAR-RSEM reference directory

    Output Parameters:
        --rsem_output       Output directory for RSEM quantification results (default: result/rsem_counts)
        --qualimap_output   Output directory for Qualimap QC results (default: result/qualimap_rnaseq)

    Example:
        nextflow run rsem_pipeline.nf \\
            --fastq_dir /path/to/fastq \\
            --star_rsem /path/to/star_rsem/reference
    """.stripIndent()
    )
}

// ====================================================================================
// PROCESSES
// ====================================================================================

process rsemCalculateExpression {
    tag "RSEM quantification with STAR for ${sample_name}"
    publishDir "${params.rsem_output}/${sample_name}", mode: 'copy'

    executor 'lsf'
    queue 'medium'
    memory '128.GB'
    cpus 28
    time '12h'
    module 'rsem/1.3.3'

    input:
    tuple val(sample_name), path(reads)

    output:
    tuple val(sample_name), path("${sample_name}.genes.results"), path("${sample_name}.isoforms.results")
    tuple val(sample_name), path("${sample_name}.stat"), emit: stats
    tuple val(sample_name), path("${sample_name}.STAR.genome.bam"), emit: bam

    script:
    """
    set +u
    eval "\$(/risapps/rhel8/miniforge3/24.5.0-0/bin/conda shell.bash hook)"
    conda activate rsem-1.3.3
    set -u

    # Create temporary directory for RSEM intermediate files
    mkdir -p ${sample_name}.temp
    mkdir -p ${sample_name}.stat

    # RSEM calculate expression with integrated STAR alignment
    # Removed --calc-ci to avoid credibility intervals calculation issues
    rsem-calculate-expression \\
        --paired-end \\
        --star \\
        --star-path /rsrch3/home/<USER>/ndeng1/software/STAR_2.7.11b/Linux_x86_64_static \\
        --star-gzipped-read-file \\
        --estimate-rspd \\
        --star-output-genome-bam \\
        --seed 12345 \\
        --temporary-folder ${sample_name}.temp \\
        -p ${task.cpus} \\
        ${reads[0]} \\
        ${reads[1]} \\
        ${params.star_rsem} \\
        ${sample_name}
    """
}

process qualimapRNAseq {
    tag "Qualimap RNA-seq QC for ${sample_name}"
    publishDir "${params.qualimap_output}/${sample_name}", mode: 'copy'

    executor 'lsf'
    queue 'medium'
    memory '128.GB'
    cpus 28
    time '24h'
    module 'qualimap/2.2.2d'

    input:
    tuple val(sample_name), path(bam_file)

    output:
    tuple val(sample_name), path("${sample_name}_qualimap")

    script:
    """
    set +u
    eval "\$(/risapps/rhel8/miniforge3/24.5.0-0/bin/conda shell.bash hook)"
    conda activate qualimap-2.2.2d
    set -u

    # Create output directory
    mkdir -p ${sample_name}_qualimap

    # Run Qualimap RNA-seq analysis
    qualimap rnaseq \\
        -bam ${bam_file} \\
        -gtf /home/<USER>/vilarsanchez/reference_new/star_rsem/GRCh38_ucsc/hg38.ncbiRefSeq.gtf \\
        -outdir ${sample_name}_qualimap \\
        -outformat HTML \\
        --java-mem-size=${task.memory.toGiga()}G
    """
}

process rsemCalculateCredibilityIntervals {
    tag "RSEM credibility intervals for ${sample_name}"
    publishDir "${params.rsem_output}/${sample_name}", mode: 'copy'

    executor 'lsf'
    queue 'medium'
    memory '64.GB'
    cpus 28
    time '6h'
    module 'rsem/1.3.3'

    input:
    tuple val(sample_name), path(genes_results), path(isoforms_results)

    output:
    tuple val(sample_name), path("${sample_name}.genes.results.ci"), path("${sample_name}.isoforms.results.ci")

    when:
    params.calc_ci == true

    script:
    """
    set +u
    eval "\$(/risapps/rhel8/miniforge3/24.5.0-0/bin/conda shell.bash hook)"
    conda activate rsem-1.3.3
    set -u

    # Calculate credibility intervals separately if needed
    rsem-calculate-credibility-intervals \\
        ${params.star_rsem} \\
        ${sample_name} \\
        ${sample_name} \\
        0.95 \\
        1000 \\
        50 \\
        1024 \\
        -p ${task.cpus} \\
        --seed 12345
    """
}

process rsemPlotModel {
    tag "RSEM plot model for ${sample_name}"
    publishDir "${params.rsem_output}/${sample_name}", mode: 'copy'

    executor 'lsf'
    queue 'short'
    memory '8.GB'
    cpus 2
    time '1h'
    module 'rsem/1.3.3'

    input:
    tuple val(sample_name), path(stat_dir)

    output:
    tuple val(sample_name), path("${sample_name}.pdf")

    script:
    """
    set +u
    eval "\$(/risapps/rhel8/miniforge3/24.5.0-0/bin/conda shell.bash hook)"
    conda activate rsem-1.3.3
    set -u

    # Generate RSEM model plots
    rsem-plot-model ${sample_name} ${sample_name}.pdf
    """
}

// ====================================================================================
// MAIN WORKFLOW
// ====================================================================================
workflow {

    // Show help message if requested
    if (params.help) {
        helpMessage()
        exit(0)
    }

    // Validate required parameters
    if (!params.fastq_dir) {
        log.error("ERROR: --fastq_dir parameter is required")
        exit(1)
    }
    if (!params.star_rsem) {
        log.error("ERROR: --star_rsem parameter is required")
        exit(1)
    }

    // Step 1: Prepare input channels
    // Get all FASTQ files from RNA-seq directory
    rnaseq_samples_ch = Channel.fromFilePairs("${params.fastq_dir}/**/*_{R1,R2}*.fastq.gz")
        .map { pair_id, files -> tuple(pair_id.split('_')[0], files) }

    // Step 2: Run RSEM quantification with integrated STAR alignment
    rsem_results_ch = rsemCalculateExpression(rnaseq_samples_ch)

    // Step 3: Optionally calculate credibility intervals separately
    if (params.calc_ci) {
        ci_results_ch = rsemCalculateCredibilityIntervals(rsem_results_ch)
    }

    // Step 4: Run Qualimap RNA-seq QC on transcript BAMs
    qualimap_results_ch = qualimapRNAseq(rsem_results_ch.bam)

    // Step 5: Generate RSEM model plots
    rsem_plot_ch = rsemPlotModel(rsem_results_ch.stats)
}
