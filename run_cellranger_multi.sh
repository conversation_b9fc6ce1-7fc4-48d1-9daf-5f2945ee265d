#!/bin/bash

# Cell Ranger Multi Pipeline Runner
# This script runs the Cell Ranger multi pipeline for scRNA-seq and TCR analysis

set -e  # Exit on any error

echo "=== Cell Ranger Multi Pipeline ==="
echo "Date: $(date)"
echo ""

# Check if Nextflow is available
if ! command -v nextflow &> /dev/null; then
    echo "ERROR: Nextflow is not available. Please load the Nextflow module or install it."
    exit 1
fi

# Check if Cell Ranger module is available
if ! module avail cellranger/9.0.1 2>&1 | grep -q "cellranger/9.0.1"; then
    echo "WARNING: Cell Ranger 9.0.1 module may not be available. Please verify."
fi

# Create output directory
OUTPUT_DIR="result/cellranger_multi"
mkdir -p "$OUTPUT_DIR"

echo "1. Starting Cell Ranger Multi pipeline..."
echo "   Output directory: $OUTPUT_DIR"
echo ""

# Run the pipeline
echo "2. Executing Nextflow pipeline..."
nextflow run cellranger_multi_pipeline.nf \
    --output_dir "$OUTPUT_DIR" \
    -with-report cellranger_multi_report.html \
    -with-timeline cellranger_multi_timeline.html \
    -with-trace cellranger_multi_trace.txt \
    -process.executor=lsf \
    -process.queue=medium

# Check if pipeline completed successfully
if [ $? -eq 0 ]; then
    echo ""
    echo "=== Pipeline completed successfully! ==="
    echo ""
    echo "Results are available in: $OUTPUT_DIR"
    echo ""
    echo "Key output files for each sample:"
    echo "  - Web summary: [sample]/outs/per_sample_outs/[sample]/web_summary.html"
    echo "  - Gene expression matrix: [sample]/outs/per_sample_outs/[sample]/count/sample_filtered_feature_bc_matrix.h5"
    echo "  - TCR annotations: [sample]/outs/per_sample_outs/[sample]/vdj_t/filtered_contig_annotations.csv"
    echo ""
    echo "Pipeline reports:"
    echo "  - Execution report: cellranger_multi_report.html"
    echo "  - Timeline: cellranger_multi_timeline.html"
    echo "  - Trace: cellranger_multi_trace.txt"
    echo "  - Summary: $OUTPUT_DIR/cellranger_multi_summary.html"
else
    echo ""
    echo "=== Pipeline failed! ==="
    echo "Check the error messages above and the trace file for details."
    exit 1
fi
