#!/bin/bash

# DRAGEN VCF Parsing Runner Script
# This script runs the DRAGEN VCF parsing using the parse_vcf_csq function

set -e  # Exit on any error

echo "=== DRAGEN VCF Parsing ==="
echo "Date: $(date)"
echo ""

# Check if R is available
if ! command -v Rscript &> /dev/null; then
    echo "ERROR: R/Rscript is not available. Please load R module or install R."
    exit 1
fi

# Check if dragen_vcf directory exists
if [ ! -d "dragen_vcf" ]; then
    echo "ERROR: dragen_vcf directory not found."
    echo "Please ensure DRAGEN VCF files are in the dragen_vcf/ directory."
    exit 1
fi

# Check if function.R exists
if [ ! -f "function.R" ]; then
    echo "ERROR: function.R not found."
    echo "Please ensure the VCF parsing function file exists."
    exit 1
fi

# Count DRAGEN VCF files
DRAGEN_FILES=$(find dragen_vcf -name "*.dragen.filtered.vcf.gz" | wc -l)
echo "Found $DRAGEN_FILES DRAGEN filtered VCF files"

if [ "$DRAGEN_FILES" -eq 0 ]; then
    echo "ERROR: No DRAGEN VCF files found with pattern *.dragen.filtered.vcf.gz"
    echo "Please check that your DRAGEN VCF files are in dragen_vcf/ directory"
    echo "and have the correct naming pattern."
    exit 1
fi

echo ""
echo "DRAGEN VCF files found:"
find dragen_vcf -name "*.dragen.filtered.vcf.gz" | head -10

echo ""
echo "Starting DRAGEN VCF parsing..."

# Run the R script
Rscript parse_dragen_vcfs.R

# Check if parsing completed successfully
if [ $? -eq 0 ]; then
    echo ""
    echo "=== DRAGEN VCF parsing completed successfully! ==="
    echo ""
    echo "Results are available in the 'results/dragen_vcf_parsed/' directory:"
    echo ""
    echo "📊 Data files:"
    echo "  - all_dragen_variants.csv: Complete parsed variant data"
    echo "  - dragen_variants_summary.csv: Summary statistics by sample"
    echo ""
    echo "📈 Plots:"
    echo "  - variants_per_sample.png: Variant counts per sample"
    echo "  - impact_distribution.png: Variant impact distribution"
    echo ""
    echo "🔍 Summary:"
    if [ -f "results/dragen_vcf_parsed/dragen_variants_summary.csv" ]; then
        echo "  Sample summary:"
        head -5 results/dragen_vcf_parsed/dragen_variants_summary.csv
    fi
else
    echo ""
    echo "=== DRAGEN VCF parsing failed! ==="
    echo "Please check the error messages above."
    exit 1
fi
