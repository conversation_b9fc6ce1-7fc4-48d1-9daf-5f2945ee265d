#!/bin/bash

# Test script for RSEM credibility intervals issue
# This script helps diagnose the RSEM credibility intervals calculation problem

echo "=== RSEM Credibility Intervals Troubleshooting Script ==="
echo "Date: $(date)"
echo ""

# Check RSEM installation
echo "1. Checking RSEM installation..."
which rsem-calculate-expression
which rsem-calculate-credibility-intervals
echo ""

# Check RSEM version
echo "2. Checking RSEM version..."
rsem-calculate-expression --version 2>&1 || echo "Could not get RSEM version"
echo ""

# Check if reference exists
RSEM_REF="/rsrch3/home/<USER>/vilarsanchez/reference_new/star_rsem/GRCh38_gencode/GRCh38"
echo "3. Checking RSEM reference files..."
echo "Reference base: $RSEM_REF"
ls -la "${RSEM_REF}"* 2>/dev/null || echo "Reference files not found or not accessible"
echo ""

# Check working directory and permissions
echo "4. Checking working directory and permissions..."
pwd
ls -la
echo ""

# Check for any existing RSEM output files that might be causing conflicts
echo "5. Checking for existing RSEM output files..."
SAMPLE_NAME="S22-E1-Nm"
echo "Sample name: $SAMPLE_NAME"
ls -la ${SAMPLE_NAME}* 2>/dev/null || echo "No existing output files found"
echo ""

# Check temporary directories
echo "6. Checking temporary directories..."
ls -la ${SAMPLE_NAME}.temp/ 2>/dev/null || echo "No temp directory found"
ls -la ${SAMPLE_NAME}.stat/ 2>/dev/null || echo "No stat directory found"
echo ""

# Test basic RSEM command without credibility intervals
echo "7. Testing basic RSEM functionality..."
echo "This would run: rsem-calculate-expression --help"
rsem-calculate-expression --help | head -10
echo ""

# Check conda environment
echo "8. Checking conda environment..."
echo "Current conda environment: $CONDA_DEFAULT_ENV"
conda list | grep rsem || echo "RSEM not found in current conda environment"
echo ""

# Check system resources
echo "9. Checking system resources..."
echo "Available memory:"
free -h
echo "Available disk space:"
df -h .
echo ""

echo "=== Troubleshooting complete ==="
echo ""
echo "RECOMMENDATIONS:"
echo "1. If RSEM reference files are missing, regenerate them with rsem-prepare-reference"
echo "2. If credibility intervals are not critical, run without --calc-ci flag"
echo "3. Check that temporary directories have write permissions"
echo "4. Ensure sufficient memory and disk space for the calculation"
echo "5. Try running with a smaller dataset first to test the setup"
